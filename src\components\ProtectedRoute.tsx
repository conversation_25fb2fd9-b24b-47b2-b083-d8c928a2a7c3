'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/providers/UserProvider'
import LoadingMask from './LoadingMask'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/en/login' 
}: ProtectedRouteProps) {
  const { userData, loading } = useUser()
  const router = useRouter()

  useEffect(() => {
    if (!loading && requireAuth && !userData) {
      router.push(redirectTo)
    }
  }, [userData, loading, requireAuth, redirectTo, router])

  if (loading) {
    return <LoadingMask className="min-h-screen" />
  }

  if (requireAuth && !userData) {
    return null // 重定向中
  }

  return <>{children}</>
}
