'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useUser } from '@/providers/UserProvider'
import SearchBar from '@/components/SearchBar'
import LoadingMask from '@/components/LoadingMask'
import LoginButton from '@/components/LoginButton'
import LogoutButton from '@/components/LogoutButton'
import CreateButton from '@/components/CreateButton'
import MindMapCard from '@/components/MindMapCard'
import Pagination from '@/components/Pagination'
import ConfirmModal from '@/components/ConfirmModal'
import ShareModal from '@/components/ShareModal'
import ProtectedRoute from '@/components/ProtectedRoute'
import connect from '@/connect'
import { MindMapItem } from '@/models/list'
import Link from 'next/link'

export default function MapListPage() {
  const params = useParams()
  const t = useTranslations()
  const { userData } = useUser()
  const [mapList, setMapList] = useState<MindMapItem[]>([])
  const [loading, setLoading] = useState(true)
  const [keyword, setKeyword] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
  })



  const type = params.type as string
  const isPublic = type === 'public'

  const fetchList = async () => {
    setLoading(true)
    try {
      const endpoint = isPublic ? '/api/public' : '/api/map'
      const res = await connect.get(endpoint, {
        params: {
          page: pagination.page,
          pageSize: pagination.pageSize,
          keyword,
        },
      })
      console.log('API Response:', res.data)
      console.log('Map List:', res.data.list)

      // 检查API返回的数据结构
      if (res.data && Array.isArray(res.data.list)) {
        setMapList(res.data.list)
        setPagination(prev => ({
          ...prev,
          total: res.data.total || 0,
        }))
      } else if (res.data && Array.isArray(res.data)) {
        // 如果API直接返回数组
        setMapList(res.data)
        setPagination(prev => ({
          ...prev,
          total: res.data.length,
        }))
      } else {
        console.warn('Unexpected API response structure:', res.data)
        setMapList([])
      }
    } catch (error) {
      console.error('Failed to fetch maps:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchList()
  }, [pagination.page, keyword, type])

  useEffect(() => {
    console.log('MapList updated:', mapList, 'Length:', mapList.length)
  }, [mapList])

  const handleSearch = (val: string) => {
    setKeyword(val)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const deleteMap = async (item: MindMapItem) => {
    if (window.confirm('Are you sure to delete this map?')) {
      try {
        await connect.delete(`/api/map/${item._id}`)
        fetchList()
      } catch (error) {
        console.error('Failed to delete map:', error)
      }
    }
  }

  const makePublic = async (item: MindMapItem) => {
    try {
      await connect.patch(`/api/map/${item._id}`, {
        public: !item.public,
      })
      item.public = !item.public
      setMapList([...mapList])
    } catch (error) {
      console.error('Failed to update map:', error)
    }
  }

  const share = (item: MindMapItem) => {
    // TODO: Implement share modal
    console.log('Share:', item)
  }

  const download = (item: MindMapItem, type: string) => {
    // TODO: Implement download functionality
    console.log('Download:', item, type)
  }

  const content = (
    <div className="mt-28">
      <SearchBar onSearch={handleSearch} />
      
      {loading ? (
        <LoadingMask className="pt-20" />
      ) : (
        <div>
          <div className="text-center mb-4">
            <p>User Data: {userData ? 'Logged in' : 'Not logged in'}</p>
            <p>Is Public: {isPublic ? 'Yes' : 'No'}</p>
            <p>Map List Length: {mapList.length}</p>
          </div>

          <div className="mx-20 grid gap-4 grid-cols-1 auto-rows-[208px] sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {!isPublic && <CreateButton className="h-full" />}
            {mapList.map((map) => (
              <Link
                key={map._id}
                href={`/${isPublic ? 'share' : 'edit'}/${map._id}`}
              >
                <MindMapCard
                  className="h-full"
                  map={map}
                  type={isPublic ? 'public' : 'private'}
                  onDelete={() => deleteMap(map)}
                  onDownload={(type) => download(map, type)}
                  onMakePublic={() => makePublic(map)}
                  onShare={() => share(map)}
                />
              </Link>
            ))}
          </div>

          <div className="flex justify-center my-8">
            <Pagination
              page={pagination.page}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
            />
          </div>
        </div>
      )}

      <div className="fixed top-5 right-8 z-20">
        {!isPublic && (userData ? <LogoutButton /> : <LoginButton />)}
      </div>
    </div>
  )

  return (
    <ProtectedRoute requireAuth={!isPublic}>
      {content}
    </ProtectedRoute>
  )
}
