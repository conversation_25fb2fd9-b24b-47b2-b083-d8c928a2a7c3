"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/list/[type]/page",{

/***/ "(app-pages-browser)/./src/components/LogoutButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/LogoutButton.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LogoutButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/providers/UserProvider */ \"(app-pages-browser)/./src/providers/UserProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction LogoutButton() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"button\");\n    const { userData, logout } = (0,_providers_UserProvider__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    if (!userData) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dropdown dropdown-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                tabIndex: 0,\n                className: \"btn btn-ghost btn-circle avatar\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: userData.avatar,\n                        alt: \"Avatar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                tabIndex: 0,\n                className: \"mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content rounded-box w-52\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    onClick: handleLogout,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        children: t(\"logout\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(LogoutButton, \"ttLQgEpNsiPqURcZ5ETuT9ESKlE=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations,\n        _providers_UserProvider__WEBPACK_IMPORTED_MODULE_1__.useUser\n    ];\n});\n_c = LogoutButton;\nvar _c;\n$RefreshReg$(_c, \"LogoutButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LogoutButton.tsx\n"));

/***/ })

});