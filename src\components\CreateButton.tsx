'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import connect from '@/connect'
import toast from '@/utils/toast'

interface CreateButtonProps {
  className?: string
}

export default function CreateButton({ className }: CreateButtonProps) {
  const t = useTranslations()
  const router = useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [title, setTitle] = useState('')
  const [creating, setCreating] = useState(false)

  const handleCreate = async () => {
    if (!title.trim()) return

    setCreating(true)
    try {
      const res = await connect.post('/api/map', {
        name: title,
      })
      setIsModalOpen(false)
      setTitle('')
      router.push(`/edit/${res.data._id}`)
    } catch (error) {
      console.error('Failed to create map:', error)
      toast.error('Failed to create map')
    } finally {
      setCreating(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCreate()
    }
  }

  return (
    <>
      <div
        className={`border rounded-2xl border-dashed hover:border-primary flex justify-center items-center flex-col cursor-pointer ${className}`}
        onClick={() => setIsModalOpen(true)}
      >
        <Image 
          src="/assets/create.svg" 
          alt="Create Mindmap" 
          width={100}
          height={100}
          className="h-2/3" 
        />
        <div>
          <span className="font-extrabold">+</span> {t('button.new')}
        </div>
      </div>

      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg">{t('misc.title')}</h3>
            <div className="pt-6">
              <input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyPress={handleKeyPress}
                type="text"
                placeholder="Type here"
                className="input input-bordered w-full"
                autoFocus
              />
            </div>
            <div className="modal-action">
              <button
                className="btn"
                onClick={() => setIsModalOpen(false)}
                disabled={creating}
              >
                {t('misc.cancel')}
              </button>
              <button
                className="btn btn-primary"
                onClick={handleCreate}
                disabled={!title.trim() || creating}
              >
                {creating && <span className="loading loading-spinner"></span>}
                {t('misc.ok')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
