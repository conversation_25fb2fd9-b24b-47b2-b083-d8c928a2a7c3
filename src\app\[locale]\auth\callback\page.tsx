'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useUser } from '@/providers/UserProvider'
import connect from '@/connect'
import toast from '@/utils/toast'

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setUserData } = useUser()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')

        if (error) {
          throw new Error(`OAuth error: ${error}`)
        }

        if (!code) {
          throw new Error('No authorization code received')
        }

        // 验证用户登录状态
        const userRes = await connect.get('/api/user')
        if (userRes.data && userRes.data.providerAccountId) {
          setUserData(userRes.data)
          setStatus('success')
          toast.success('Login successful!')
          
          // 延迟跳转，让用户看到成功消息
          setTimeout(() => {
            router.push('/en/list/map')
          }, 1500)
        } else {
          throw new Error('Failed to get user data')
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        setStatus('error')
        toast.error('Login failed')
        
        // 延迟跳转到登录页面
        setTimeout(() => {
          router.push('/en/login')
        }, 2000)
      }
    }

    handleCallback()
  }, [searchParams, setUserData, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 text-center">
        {status === 'loading' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <h2 className="text-2xl font-bold text-gray-900">Processing login...</h2>
            <p className="text-gray-600">Please wait while we complete your authentication.</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <div className="text-green-600">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Login Successful!</h2>
            <p className="text-gray-600">Redirecting to your dashboard...</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <div className="text-red-600">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Login Failed</h2>
            <p className="text-gray-600">Something went wrong. Redirecting to login page...</p>
          </>
        )}
      </div>
    </div>
  )
}
