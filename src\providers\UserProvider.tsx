'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User } from '@/models/user'
import { Response } from '@/models/response'
import connect from '@/connect'

interface UserContextType {
  userData: User | undefined
  setUserData: (user: User | undefined) => void
  loading: boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: ReactNode }) {
  const [userData, setUserData] = useState<User | undefined>(undefined)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await connect.get<never, Response<User>>('/api/user')
        if (res.data && res.data.providerAccountId) {
          setUserData(res.data)
        }
      } catch (error) {
        console.error('Failed to fetch user:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUser()
  }, [])

  return (
    <UserContext.Provider value={{ userData, setUserData, loading }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
