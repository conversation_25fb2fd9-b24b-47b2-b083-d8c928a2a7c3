'use client'

import { useTranslations } from 'next-intl'
import { useUser } from '@/providers/UserProvider'

export default function LogoutButton() {
  const t = useTranslations('button')
  const { userData, logout } = useUser()

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (!userData) return null

  return (
    <div className="dropdown dropdown-end">
      <label tabIndex={0} className="btn btn-ghost btn-circle avatar">
        <div className="w-10 rounded-full">
          <img src={userData.avatar} alt="Avatar" />
        </div>
      </label>
      <ul
        tabIndex={0}
        className="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content rounded-box w-52"
      >
        <li onClick={handleLogout}>
          <a>{t('logout')}</a>
        </li>
      </ul>
    </div>
  )
}
