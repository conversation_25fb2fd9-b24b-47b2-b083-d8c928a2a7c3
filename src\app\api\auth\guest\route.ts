import { NextRequest, NextResponse } from 'next/server'

// 模拟guest用户数据
const mockGuestUser = {
  _id: 'guest-user-id',
  id: 'guest-user-id',
  name: 'Guest User',
  email: '<EMAIL>',
  avatar: 'https://ui-avatars.com/api/?name=Guest&background=6366f1&color=fff',
  from: 'guest',
  providerAccountId: 'guest-provider-id'
}

export async function POST(request: NextRequest) {
  try {
    // 创建响应并设置cookie
    const response = NextResponse.json({
      success: true,
      data: mockGuestUser,
      message: 'Guest login successful'
    })

    // 设置认证cookie (7天过期)
    response.cookies.set('auth-token', 'guest-token-123', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 // 7 days
    })

    response.cookies.set('user-session', 'guest-session', {
      httpOnly: false, // 允许客户端访问
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 // 7 days
    })

    return response
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Guest login failed'
    }, { status: 500 })
  }
}
