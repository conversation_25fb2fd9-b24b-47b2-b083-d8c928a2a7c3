"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEM7QUFDVTs7QUFFcEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4yOV9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzP2IwMDIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiLi4vc2hhcmVkL2xpYi9hcHAtZHluYW1pY1wiO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtZHluYW1pYy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYXBwLWR5bmFtaWMuanMiLCJtYXBwaW5ncyI6Ijs7OzsyQ0FpQ0E7OztlQUF3QkE7Ozs7OzRFQWpDTjsrRUFDRztBQWdDTixTQUFTQSxRQUN0QkMsY0FBNkMsRUFDN0NDLE9BQTJCO1FBbUNoQkM7SUFqQ1gsSUFBSUMsa0JBQXNDO1FBQ3hDLHdEQUF3RDtRQUN4REMsU0FBUyxDQUFBQztnQkFBQyxFQUFFQyxLQUFLLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFLEdBQUFIO1lBQ3ZDLElBQUksQ0FBQ0csV0FBVyxPQUFPO1lBQ3ZCLElBQUlDLElBQXlCLEVBQWM7Z0JBQ3pDLElBQUlGLFdBQVc7b0JBQ2IsT0FBTztnQkFDVDtnQkFDQSxJQUFJRCxPQUFPO29CQUNULE9BQ0UsV0FERixHQUNFLElBQUFJLFlBQUFDLElBQUEsRUFBQ0MsS0FBQUE7OzRCQUNFTixNQUFNTyxPQUFPOzBDQUNkLElBQUFILFlBQUFJLEdBQUEsRUFBQ0MsTUFBQUEsQ0FBQUE7NEJBQ0FULE1BQU1VLEtBQUs7OztnQkFHbEI7WUFDRjtZQUNBLE9BQU87UUFDVDtJQUNGO0lBRUEsSUFBSSxPQUFPaEIsbUJBQW1CLFlBQVk7UUFDeENHLGdCQUFnQmMsTUFBTSxHQUFHakI7SUFDM0I7SUFFQSxNQUFNRSxnQkFBZ0I7UUFDcEIsR0FBR0MsZUFBZTtRQUNsQixHQUFHRixPQUFPO0lBQ1o7SUFFQSxPQUFPaUIsQ0FBQUEsR0FBQUEsVUFBQUEsT0FBUSxFQUFDO1FBQ2QsR0FBR2hCLGFBQWE7UUFDaEJpQixTQUFPLENBQUVqQixtQ0FBQUEsY0FBY2tCLGlCQUFpQixxQkFBL0JsQixpQ0FBaUNpQixPQUFPO0lBQ25EO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL2FwcC1keW5hbWljLnRzeD85NDcwIl0sIm5hbWVzIjpbImR5bmFtaWMiLCJkeW5hbWljT3B0aW9ucyIsIm9wdGlvbnMiLCJtZXJnZWRPcHRpb25zIiwibG9hZGFibGVPcHRpb25zIiwibG9hZGluZyIsInBhcmFtIiwiZXJyb3IiLCJpc0xvYWRpbmciLCJwYXN0RGVsYXkiLCJwcm9jZXNzIiwiX2pzeHJ1bnRpbWUiLCJqc3hzIiwicCIsIm1lc3NhZ2UiLCJqc3giLCJiciIsInN0YWNrIiwibG9hZGVyIiwiTG9hZGFibGUiLCJtb2R1bGVzIiwibG9hZGFibGVHZW5lcmF0ZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**************************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2R5bmFtaWMtYmFpbG91dC10by1jc3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQWNPLE1BQUFBLGdCQUFzQkMsbUJBQUFBLENBQXVDO1NBQXZDQyxhQUFVQyxLQUFRO0lBQzdDLElBQUksRUFBQUMsTUFBT0MsRUFBQUEsUUFBVyxLQUFBQztRQUNwQixPQUFNRCxXQUFJRSxhQUFBQTtRQUNaLFVBQUFQLGNBQUFPLGlCQUFBLENBQUFIO0lBRUE7SUFDRixPQUFBRDs7S0FONkJEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci50c3g/ZTI2NCJdLCJuYW1lcyI6WyJfYmFpbG91dHRvY3NyIiwicmVxdWlyZSIsIkJhaWxvdXRUb0NTUiIsImNoaWxkcmVuIiwicmVhc29uIiwid2luZG93IiwicGFyYW0iLCJCYWlsb3V0VG9DU1JFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \***************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMjlfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL3ByZWxvYWQtY3NzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBSU8sTUFBQUEsK0JBQXNFQyxtQkFBQUEsQ0FBQTtTQUFsREMsV0FBV0MsS0FBWDtJQUN6QixNQUFBQyxTQUFBLEtBQUFEO0lBQ0EsK0VBQW1DO1FBQ2pDLE9BQU9FLFdBQUE7UUFDVDtJQUVBO0lBQ0EsTUFBTUMsZUFBYSxJQUFBTiw2QkFBQU8sdUJBQUE7SUFFbkIsTUFBQUQsV0FBQTtJQUNBLDRFQUFrRDtJQUNsRCxrREFBMENGO1FBQ3hDSSxhQUFNQyxxQkFBd0JDLElBQUFBLFdBQUFBO1FBQzlCLE1BQUtELFdBQU1FLGFBQWtCRCxxQkFBQTthQUMzQixNQUFLRCxPQUFTRSxVQUFNO1lBQ3BCLEtBQUFGLFFBQU1HLENBQUFBLElBQVdILEVBQUFBO1lBR2pCSCxNQUFBQSxXQUFpQk0sUUFBQUEsQ0FBQUEsSUFBQUEsQ0FBQUEsS0FBQUEsQ0FBQUEsTUFBQUEsQ0FBQUEsQ0FBQUEsT0FBQUEsS0FBQUEsUUFBQUEsQ0FBQUE7WUFDbkJOLFNBQUFPLElBQUEsSUFBQUQ7UUFDRjtJQUVBO1FBQ0VOLFNBQU9RLE1BQUE7UUFDVDtJQUVBO1dBRUtSLFdBQUFBLEdBQUFBLENBQUFBLEdBQVNTLFlBQUtDLEdBQUFBLEVBQUFBLFlBQUFBLFFBQUFBLEVBQUFBO2tCQUNiVixTQUFBUyxHQUFBLEVBQUFDO21CQUdpQixrQkFBQUMsWUFBQUMsR0FBQTtnQkFDYkMsYUFBWTtnQkFDWkMsWUFBSTtnQkFDSkMsS0FBQUE7Z0JBQ0FDLE1BQUdkLGFBQUFlLFdBQUEsZUFBQUMsVUFBQVI7Z0JBTEVBLElBQUFBO1lBUVgsR0FBQUE7O0lBR047O0tBMUMyQmQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3NyYy9zaGFyZWQvbGliL2xhenktZHluYW1pYy9wcmVsb2FkLWNzcy50c3g/M2U5MCJdLCJuYW1lcyI6WyJfcmVxdWVzdGFzeW5jc3RvcmFnZWV4dGVybmFsIiwicmVxdWlyZSIsIlByZWxvYWRDc3MiLCJwYXJhbSIsIm1vZHVsZUlkcyIsIndpbmRvdyIsImFsbEZpbGVzIiwiZ2V0RXhwZWN0ZWRSZXF1ZXN0U3RvcmUiLCJyZXF1ZXN0U3RvcmUiLCJtYW5pZmVzdCIsInJlYWN0TG9hZGFibGVNYW5pZmVzdCIsImtleSIsImNzc0ZpbGVzIiwicHVzaCIsImxlbmd0aCIsIm1hcCIsImZpbGUiLCJfanN4cnVudGltZSIsImpzeCIsInByZWNlZGVuY2UiLCJyZWwiLCJocmVmIiwiYXMiLCJhc3NldFByZWZpeCIsImVuY29kZVVSSSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mind-elixir/example */ \"(app-pages-browser)/./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/example.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MindElixirReact_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MindElixirReact */ \"(app-pages-browser)/./src/components/MindElixirReact.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 18\n        }, undefined)\n});\n_c = MindElixirReact;\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n            data: mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c1 = HomePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact\");\n$RefreshReg$(_c1, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFa0M7QUFDSTtBQUV0QyxNQUFNRSxrQkFBa0JGLHdEQUFPQSxDQUFDLElBQU0sa1BBQU87Ozs7OztJQUMzQ0csS0FBSztJQUNMQyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVO3NCQUE0Qzs7Ozs7OztLQUZ0RUo7QUFLUyxTQUFTSztJQUN0QixxQkFDRSw4REFBQ0Y7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0o7WUFBZ0JELE1BQU1BLDJEQUFJQTs7Ozs7Ozs7Ozs7QUFHakM7TUFOd0JNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvW2xvY2FsZV0vcGFnZS50c3g/YTQ3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJ1xuaW1wb3J0IGRhdGEgZnJvbSAnbWluZC1lbGl4aXIvZXhhbXBsZSdcblxuY29uc3QgTWluZEVsaXhpclJlYWN0ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9NaW5kRWxpeGlyUmVhY3QnKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+TG9hZGluZy4uLjwvZGl2PlxufSlcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlblwiPlxuICAgICAgPE1pbmRFbGl4aXJSZWFjdCBkYXRhPXtkYXRhfSAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiZHluYW1pYyIsImRhdGEiLCJNaW5kRWxpeGlyUmVhY3QiLCJzc3IiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiSG9tZVBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/page.tsx\n"));

/***/ })

});