'use client'

import { useTranslations } from 'next-intl'

export default function LoginButton() {
  const t = useTranslations('button')

  const githubLogin = () => {
    window.location.href = 'http://localhost:7001' + '/oauth/github/login'
  }

  const googleLogin = () => {
    window.location.href = 'http://localhost:7001' + '/oauth/google/login'
  }

  return (
    <div className="dropdown dropdown-end">
      <label tabIndex={0} className="btn btn-primary">
        {t('signin')}
      </label>
      <ul
        tabIndex={0}
        className="dropdown-content z-[1] menu p-2 shadow rounded-box w-52 bg-base-100"
      >
        <li onClick={githubLogin}>
          <a>{t('signinWithGitHub')}</a>
        </li>
        <li onClick={googleLogin}>
          <a>Sign in with Google</a>
        </li>
      </ul>
    </div>
  )
}
