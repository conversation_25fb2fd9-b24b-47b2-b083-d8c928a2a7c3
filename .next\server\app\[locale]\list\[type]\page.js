/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/list/[type]/page";
exports.ids = ["app/[locale]/list/[type]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./cn.json": [
		"(rsc)/./messages/cn.json",
		"_rsc_messages_cn_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&page=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&page=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1d1c\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'list',\n        {\n        children: [\n        '[type]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/list/[type]/page.tsx */ \"(rsc)/./src/app/[locale]/list/[type]/page.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/list/[type]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/list/[type]/page\",\n        pathname: \"/[locale]/list/[type]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&page=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/NavBar.tsx */ \"(ssr)/./src/components/NavBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/UserProvider.tsx */ \"(ssr)/./src/providers/UserProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clist%5C%5C%5Btype%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clist%5C%5C%5Btype%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/list/[type]/page.tsx */ \"(ssr)/./src/app/[locale]/list/[type]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjI5X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDZ2l0JTVDJTVDbWluZC1lbGl4aXItY2xvdWQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUNsaXN0JTVDJTVDJTVCdHlwZSU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBMkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8/OGI0YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGdpdFxcXFxtaW5kLWVsaXhpci1jbG91ZFxcXFxzcmNcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXGxpc3RcXFxcW3R5cGVdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Clist%5C%5C%5Btype%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/[locale]/list/[type]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/list/[type]/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/UserProvider */ \"(ssr)/./src/providers/UserProvider.tsx\");\n/* harmony import */ var _components_SearchBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SearchBar */ \"(ssr)/./src/components/SearchBar.tsx\");\n/* harmony import */ var _components_LoadingMask__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingMask */ \"(ssr)/./src/components/LoadingMask.tsx\");\n/* harmony import */ var _components_LoginButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoginButton */ \"(ssr)/./src/components/LoginButton.tsx\");\n/* harmony import */ var _components_LogoutButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LogoutButton */ \"(ssr)/./src/components/LogoutButton.tsx\");\n/* harmony import */ var _components_CreateButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CreateButton */ \"(ssr)/./src/components/CreateButton.tsx\");\n/* harmony import */ var _components_MindMapCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/MindMapCard */ \"(ssr)/./src/components/MindMapCard.tsx\");\n/* harmony import */ var _components_Pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/Pagination */ \"(ssr)/./src/components/Pagination.tsx\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/connect */ \"(ssr)/./src/connect.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MapListPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_13__.useTranslations)();\n    const { userData } = (0,_providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [mapList, setMapList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const type = params.type;\n    const isPublic = type === \"public\";\n    const fetchList = async ()=>{\n        setLoading(true);\n        try {\n            const endpoint = isPublic ? \"/api/public\" : \"/api/map\";\n            const res = await _connect__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(endpoint, {\n                params: {\n                    page: pagination.page,\n                    pageSize: pagination.pageSize,\n                    keyword\n                }\n            });\n            console.log(\"API Response:\", res.data);\n            console.log(\"Map List:\", res.data.list);\n            // 检查API返回的数据结构\n            if (res.data && Array.isArray(res.data.list)) {\n                setMapList(res.data.list);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: res.data.total || 0\n                    }));\n            } else if (res.data && Array.isArray(res.data)) {\n                // 如果API直接返回数组\n                setMapList(res.data);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: res.data.length\n                    }));\n            } else {\n                console.warn(\"Unexpected API response structure:\", res.data);\n                setMapList([]);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch maps:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchList();\n    }, [\n        pagination.page,\n        keyword,\n        type\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"MapList updated:\", mapList, \"Length:\", mapList.length);\n    }, [\n        mapList\n    ]);\n    const handleSearch = (val)=>{\n        setKeyword(val);\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const deleteMap = async (item)=>{\n        if (window.confirm(\"Are you sure to delete this map?\")) {\n            try {\n                await _connect__WEBPACK_IMPORTED_MODULE_11__[\"default\"].delete(`/api/map/${item._id}`);\n                fetchList();\n            } catch (error) {\n                console.error(\"Failed to delete map:\", error);\n            }\n        }\n    };\n    const makePublic = async (item)=>{\n        try {\n            await _connect__WEBPACK_IMPORTED_MODULE_11__[\"default\"].patch(`/api/map/${item._id}`, {\n                public: !item.public\n            });\n            item.public = !item.public;\n            setMapList([\n                ...mapList\n            ]);\n        } catch (error) {\n            console.error(\"Failed to update map:\", error);\n        }\n    };\n    const share = (item)=>{\n        // TODO: Implement share modal\n        console.log(\"Share:\", item);\n    };\n    const download = (item, type)=>{\n        // TODO: Implement download functionality\n        console.log(\"Download:\", item, type);\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onSearch: handleSearch\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingMask__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"pt-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"User Data: \",\n                                    userData ? \"Logged in\" : \"Not logged in\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Is Public: \",\n                                    isPublic ? \"Yes\" : \"No\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Map List Length: \",\n                                    mapList.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-20 grid gap-4 grid-cols-1 auto-rows-[208px] sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\",\n                        children: [\n                            !isPublic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CreateButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 27\n                            }, this),\n                            mapList.map((map)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    href: `/${isPublic ? \"share\" : \"edit\"}/${map._id}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapCard__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-full\",\n                                        map: map,\n                                        type: isPublic ? \"public\" : \"private\",\n                                        onDelete: ()=>deleteMap(map),\n                                        onDownload: (type)=>download(map, type),\n                                        onMakePublic: ()=>makePublic(map),\n                                        onShare: ()=>share(map)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, map._id, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Pagination__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            page: pagination.page,\n                            pageSize: pagination.pageSize,\n                            total: pagination.total,\n                            onPageChange: (page)=>setPagination((prev)=>({\n                                        ...prev,\n                                        page\n                                    }))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-5 right-8 z-20\",\n                children: !isPublic && (userData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LogoutButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 35\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 54\n                }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\list\\\\[type]\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n    return content;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/list/[type]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CreateButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/CreateButton.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/connect */ \"(ssr)/./src/connect.ts\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/toast */ \"(ssr)/./src/utils/toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction CreateButton({ className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCreate = async ()=>{\n        if (!title.trim()) return;\n        setCreating(true);\n        try {\n            const res = await _connect__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/api/map\", {\n                name: title\n            });\n            setIsModalOpen(false);\n            setTitle(\"\");\n            router.push(`/edit/${res.data._id}`);\n        } catch (error) {\n            console.error(\"Failed to create map:\", error);\n            _utils_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to create map\");\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            handleCreate();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `border rounded-2xl border-dashed hover:border-primary flex justify-center items-center flex-col cursor-pointer ${className}`,\n                onClick: ()=>setIsModalOpen(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: \"/assets/create.svg\",\n                        alt: \"Create Mindmap\",\n                        width: 100,\n                        height: 100,\n                        className: \"h-2/3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-extrabold\",\n                                children: \"+\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            t(\"button.new\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal modal-open\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"modal-box\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-bold text-lg\",\n                            children: t(\"misc.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: title,\n                                onChange: (e)=>setTitle(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                type: \"text\",\n                                placeholder: \"Type here\",\n                                className: \"input input-bordered w-full\",\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"modal-action\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn\",\n                                    onClick: ()=>setIsModalOpen(false),\n                                    disabled: creating,\n                                    children: t(\"misc.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-primary\",\n                                    onClick: handleCreate,\n                                    disabled: !title.trim() || creating,\n                                    children: [\n                                        creating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"loading loading-spinner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 30\n                                        }, this),\n                                        t(\"misc.ok\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\CreateButton.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CreateButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingMask.tsx":
/*!****************************************!*\
  !*** ./src/components/LoadingMask.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingMask)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction LoadingMask({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex justify-center items-center ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"loading loading-spinner loading-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoadingMask.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoadingMask.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2FkaW5nTWFzay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUllLFNBQVNBLFlBQVksRUFBRUMsU0FBUyxFQUFvQjtJQUNqRSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLGlDQUFpQyxFQUFFQSxVQUFVLENBQUM7a0JBQzdELDRFQUFDRTtZQUFLRixXQUFVOzs7Ozs7Ozs7OztBQUd0QiIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vc3JjL2NvbXBvbmVudHMvTG9hZGluZ01hc2sudHN4PzNmYmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW50ZXJmYWNlIExvYWRpbmdNYXNrUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZ01hc2soeyBjbGFzc05hbWUgfTogTG9hZGluZ01hc2tQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJsb2FkaW5nIGxvYWRpbmctc3Bpbm5lciBsb2FkaW5nLWxnXCI+PC9zcGFuPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZ01hc2siLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingMask.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoginButton.tsx":
/*!****************************************!*\
  !*** ./src/components/LoginButton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LoginButton() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"button\");\n    const githubLogin = ()=>{\n        window.location.href = \"http://localhost:7001\" + \"/oauth/github/login\";\n    };\n    const googleLogin = ()=>{\n        window.location.href = \"http://localhost:7001\" + \"/oauth/google/login\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dropdown dropdown-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                tabIndex: 0,\n                className: \"btn btn-primary\",\n                children: t(\"signin\")\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                tabIndex: 0,\n                className: \"dropdown-content z-[1] menu p-2 shadow rounded-box w-52 bg-base-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: githubLogin,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            children: t(\"signinWithGitHub\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        onClick: googleLogin,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            children: \"Sign in with Google\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LoginButton.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoginButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LogoutButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/LogoutButton.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogoutButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/providers/UserProvider */ \"(ssr)/./src/providers/UserProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LogoutButton() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"button\");\n    const { userData, logout } = (0,_providers_UserProvider__WEBPACK_IMPORTED_MODULE_1__.useUser)();\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n        }\n    };\n    if (!userData) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dropdown dropdown-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                tabIndex: 0,\n                className: \"btn btn-ghost btn-circle avatar\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-10 rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: userData.avatar,\n                        alt: \"Avatar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                tabIndex: 0,\n                className: \"mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content rounded-box w-52\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    onClick: handleLogout,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        children: t(\"logout\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\LogoutButton.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2dvdXRCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMkM7QUFDTztBQUVuQyxTQUFTRTtJQUN0QixNQUFNQyxJQUFJSCwwREFBZUEsQ0FBQztJQUMxQixNQUFNLEVBQUVJLFFBQVEsRUFBRUMsTUFBTSxFQUFFLEdBQUdKLGdFQUFPQTtJQUVwQyxNQUFNSyxlQUFlO1FBQ25CLElBQUk7WUFDRixNQUFNRDtRQUNSLEVBQUUsT0FBT0UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0JBQWtCQTtRQUNsQztJQUNGO0lBRUEsSUFBSSxDQUFDSCxVQUFVLE9BQU87SUFFdEIscUJBQ0UsOERBQUNLO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBTUMsVUFBVTtnQkFBR0YsV0FBVTswQkFDNUIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRzt3QkFBSUMsS0FBS1YsU0FBU1csTUFBTTt3QkFBRUMsS0FBSTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHbkMsOERBQUNDO2dCQUNDTCxVQUFVO2dCQUNWRixXQUFVOzBCQUVWLDRFQUFDUTtvQkFBR0MsU0FBU2I7OEJBQ1gsNEVBQUNjO2tDQUFHakIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtoQiIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vc3JjL2NvbXBvbmVudHMvTG9nb3V0QnV0dG9uLnRzeD9lZjI3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbnMgfSBmcm9tICduZXh0LWludGwnXG5pbXBvcnQgeyB1c2VVc2VyIH0gZnJvbSAnQC9wcm92aWRlcnMvVXNlclByb3ZpZGVyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2dvdXRCdXR0b24oKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoJ2J1dHRvbicpXG4gIGNvbnN0IHsgdXNlckRhdGEsIGxvZ291dCB9ID0gdXNlVXNlcigpXG5cbiAgY29uc3QgaGFuZGxlTG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBsb2dvdXQoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZmFpbGVkOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGlmICghdXNlckRhdGEpIHJldHVybiBudWxsXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duIGRyb3Bkb3duLWVuZFwiPlxuICAgICAgPGxhYmVsIHRhYkluZGV4PXswfSBjbGFzc05hbWU9XCJidG4gYnRuLWdob3N0IGJ0bi1jaXJjbGUgYXZhdGFyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICA8aW1nIHNyYz17dXNlckRhdGEuYXZhdGFyfSBhbHQ9XCJBdmF0YXJcIiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbGFiZWw+XG4gICAgICA8dWxcbiAgICAgICAgdGFiSW5kZXg9ezB9XG4gICAgICAgIGNsYXNzTmFtZT1cIm10LTMgei1bMV0gcC0yIHNoYWRvdyBtZW51IG1lbnUtc20gZHJvcGRvd24tY29udGVudCByb3VuZGVkLWJveCB3LTUyXCJcbiAgICAgID5cbiAgICAgICAgPGxpIG9uQ2xpY2s9e2hhbmRsZUxvZ291dH0+XG4gICAgICAgICAgPGE+e3QoJ2xvZ291dCcpfTwvYT5cbiAgICAgICAgPC9saT5cbiAgICAgIDwvdWw+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VUcmFuc2xhdGlvbnMiLCJ1c2VVc2VyIiwiTG9nb3V0QnV0dG9uIiwidCIsInVzZXJEYXRhIiwibG9nb3V0IiwiaGFuZGxlTG9nb3V0IiwiZXJyb3IiLCJjb25zb2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwibGFiZWwiLCJ0YWJJbmRleCIsImltZyIsInNyYyIsImF2YXRhciIsImFsdCIsInVsIiwibGkiLCJvbkNsaWNrIiwiYSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LogoutButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MindMapCard.tsx":
/*!****************************************!*\
  !*** ./src/components/MindMapCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MindMapCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MindMapCard.tsx -> \" + \"./MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full w-full bg-gray-100 animate-pulse\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n            lineNumber: 10,\n            columnNumber: 18\n        }, undefined)\n});\nfunction MindMapCard({ map, type, className, onDelete, onDownload, onMakePublic, onShare }) {\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const options = {\n        el: \"\",\n        direction: 2,\n        draggable: false,\n        editable: false,\n        contextMenu: false,\n        toolBar: false,\n        keypress: false\n    };\n    const timeFormatter = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `card box-content bg-base-100 shadow ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                className: \"w-full aspect-video\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n                    data: map.content,\n                    options: options,\n                    initScale: 0.2,\n                    className: \"h-full w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"m-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold overflow-hidden whitespace-nowrap text-ellipsis\",\n                                children: map.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            map.public && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"badge ml-1\",\n                                children: \"Public\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 26\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: timeFormatter(map.updatedAt || map.date)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            type === \"private\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dropdown dropdown-end absolute right-2 top-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        tabIndex: 0,\n                        className: \"btn btn-ghost btn-circle btn-sm\",\n                        onClick: (e)=>{\n                            e.preventDefault();\n                            setShowDropdown(!showDropdown);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        tabIndex: 0,\n                        className: \"dropdown-content z-[1] menu p-2 shadow rounded-box w-52 bg-base-100\",\n                        onClick: (e)=>e.preventDefault(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: onMakePublic,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    children: map.public ? \"Make Private\" : \"Make Public\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: onShare,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    children: \"Share\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"p-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    onClick: ()=>onDownload(\"json\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        children: \"JSON\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    onClick: ()=>onDownload(\"html\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        children: \"HTML\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    onClick: ()=>onDownload(\"xmind\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        children: \"XMind\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                onClick: onDelete,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"text-error\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindMapCard.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MindMapCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NavBar.tsx":
/*!***********************************!*\
  !*** ./src/components/NavBar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NavBar({ className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const availableLocales = [\n        \"en\",\n        \"cn\",\n        \"ja\"\n    ];\n    const changeLocale = (newLocale)=>{\n        const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);\n        router.push(newPath);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `navbar bg-base-100 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                tabIndex: 0,\n                                className: \"btn btn-ghost lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M4 6h16M4 12h8m-8 6h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                tabIndex: 0,\n                                className: \"menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow rounded-box w-52\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/list/public\",\n                                            children: t(\"menu.public\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/list/map\",\n                                            children: t(\"menu.folder\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/about\",\n                                            children: t(\"menu.about\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        tabIndex: 0,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    children: \"i18n\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"p-2\",\n                                                    children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            onClick: ()=>changeLocale(localeOption),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                children: localeOption\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, localeOption, false, {\n                                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"btn btn-ghost normal-case text-xl !pl-0\",\n                        href: \"/\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: \"/logo2.png\",\n                                alt: \"Logo\",\n                                width: 32,\n                                height: 32,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            \"Mind Elixir\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-center hidden lg:flex\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"menu menu-horizontal px-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/list/public\",\n                                children: t(\"menu.public\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/list/map\",\n                                children: t(\"menu.folder\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/about\",\n                                children: t(\"menu.about\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            tabIndex: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        children: \"i18n\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"p-2\",\n                                        children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                onClick: ()=>changeLocale(localeOption),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    children: localeOption\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, localeOption, false, {\n                                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-end\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NavBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Pagination.tsx":
/*!***************************************!*\
  !*** ./src/components/Pagination.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pagination)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Pagination({ page, pageSize, total, onPageChange }) {\n    const totalPages = Math.ceil(total / pageSize);\n    if (totalPages <= 1) return null;\n    const getVisiblePages = ()=>{\n        const delta = 2;\n        const range = [];\n        const rangeWithDots = [];\n        for(let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++){\n            range.push(i);\n        }\n        if (page - delta > 2) {\n            rangeWithDots.push(1, \"...\");\n        } else {\n            rangeWithDots.push(1);\n        }\n        rangeWithDots.push(...range);\n        if (page + delta < totalPages - 1) {\n            rangeWithDots.push(\"...\", totalPages);\n        } else {\n            rangeWithDots.push(totalPages);\n        }\n        return rangeWithDots;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"join\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"join-item btn\",\n                onClick: ()=>onPageChange(page - 1),\n                disabled: page <= 1,\n                children: \"\\xab\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            getVisiblePages().map((pageNum, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: `join-item btn ${pageNum === page ? \"btn-active\" : \"\"} ${pageNum === \"...\" ? \"btn-disabled\" : \"\"}`,\n                    onClick: ()=>typeof pageNum === \"number\" && onPageChange(pageNum),\n                    disabled: pageNum === \"...\",\n                    children: pageNum\n                }, index, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"join-item btn\",\n                onClick: ()=>onPageChange(page + 1),\n                disabled: page >= totalPages,\n                children: \"\\xbb\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\Pagination.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Pagination.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SearchBar.tsx":
/*!**************************************!*\
  !*** ./src/components/SearchBar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction SearchBar({ onSearch, className }) {\n    const handleChange = (e)=>{\n        onSearch(e.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `max-w-md mx-auto mb-5 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid place-items-center h-full w-12 text-gray-300\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-6 w-6\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    className: \"input input-bordered w-full max-w-xs\",\n                    type: \"text\",\n                    id: \"search\",\n                    placeholder: \"Search something..\",\n                    onChange: handleChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\SearchBar.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZWFyY2hCYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFPZSxTQUFTQSxVQUFVLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFrQjtJQUN2RSxNQUFNQyxlQUFlLENBQUNDO1FBQ3BCSCxTQUFTRyxFQUFFQyxNQUFNLENBQUNDLEtBQUs7SUFDekI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUwsV0FBVyxDQUFDLHNCQUFzQixFQUFFQSxVQUFVLENBQUM7a0JBQ2xELDRFQUFDSztZQUFJTCxXQUFVOzs4QkFDYiw4REFBQ0s7b0JBQUlMLFdBQVU7OEJBQ2IsNEVBQUNNO3dCQUNDQyxPQUFNO3dCQUNOUCxXQUFVO3dCQUNWUSxNQUFLO3dCQUNMQyxTQUFRO3dCQUNSQyxRQUFPO2tDQUVQLDRFQUFDQzs0QkFDQ0MsZUFBYzs0QkFDZEMsZ0JBQWU7NEJBQ2ZDLGFBQVk7NEJBQ1pDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSVIsOERBQUNDO29CQUNDaEIsV0FBVTtvQkFDVmlCLE1BQUs7b0JBQ0xDLElBQUc7b0JBQ0hDLGFBQVk7b0JBQ1pDLFVBQVVuQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8uL3NyYy9jb21wb25lbnRzL1NlYXJjaEJhci50c3g/MjE3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW50ZXJmYWNlIFNlYXJjaEJhclByb3BzIHtcbiAgb25TZWFyY2g6ICh2YWx1ZTogc3RyaW5nKSA9PiB2b2lkXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZWFyY2hCYXIoeyBvblNlYXJjaCwgY2xhc3NOYW1lIH06IFNlYXJjaEJhclByb3BzKSB7XG4gIGNvbnN0IGhhbmRsZUNoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIG9uU2VhcmNoKGUudGFyZ2V0LnZhbHVlKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1heC13LW1kIG14LWF1dG8gbWItNSAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIHBsYWNlLWl0ZW1zLWNlbnRlciBoLWZ1bGwgdy0xMiB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJoLTYgdy02XCJcbiAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcbiAgICAgICAgICAgICAgZD1cIk0yMSAyMWwtNi02bTItNWE3IDcgMCAxMS0xNCAwIDcgNyAwIDAxMTQgMHpcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0IGlucHV0LWJvcmRlcmVkIHctZnVsbCBtYXgtdy14c1wiXG4gICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgIGlkPVwic2VhcmNoXCJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBzb21ldGhpbmcuLlwiXG4gICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiU2VhcmNoQmFyIiwib25TZWFyY2giLCJjbGFzc05hbWUiLCJoYW5kbGVDaGFuZ2UiLCJlIiwidGFyZ2V0IiwidmFsdWUiLCJkaXYiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImlucHV0IiwidHlwZSIsImlkIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SearchBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/connect.ts":
/*!************************!*\
  !*** ./src/connect.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.7.7/node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/toast */ \"(ssr)/./src/utils/toast.ts\");\n\n\nconst relink = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"http://localhost:7001\",\n    withCredentials: true\n});\nrelink.interceptors.response.use(function(res) {\n    return res.data;\n}, function(error) {\n    if (error.response.status !== 401) {\n        _utils_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"Network error\");\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (relink);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29ubmVjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUI7QUFDUTtBQUVqQyxNQUFNRSxTQUFTRiw2Q0FBS0EsQ0FBQ0csTUFBTSxDQUFDO0lBQzFCQyxTQUFTO0lBQ1RDLGlCQUFpQjtBQUNuQjtBQUVBSCxPQUFPSSxZQUFZLENBQUNDLFFBQVEsQ0FBQ0MsR0FBRyxDQUM5QixTQUFVQyxHQUFHO0lBQ1gsT0FBT0EsSUFBSUMsSUFBSTtBQUNqQixHQUNBLFNBQVVDLEtBQUs7SUFDYixJQUFJQSxNQUFNSixRQUFRLENBQUNLLE1BQU0sS0FBSyxLQUFLO1FBQ2pDWCxvREFBS0EsQ0FBQ1UsS0FBSyxDQUFDO0lBQ2Q7SUFDQSxPQUFPRSxRQUFRQyxNQUFNLENBQUNIO0FBQ3hCO0FBR0YsaUVBQWVULE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8uL3NyYy9jb25uZWN0LnRzPzMyMmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJ1xyXG5pbXBvcnQgdG9hc3QgZnJvbSAnLi91dGlscy90b2FzdCdcclxuXHJcbmNvbnN0IHJlbGluayA9IGF4aW9zLmNyZWF0ZSh7XHJcbiAgYmFzZVVSTDogJ2h0dHA6Ly9sb2NhbGhvc3Q6NzAwMScsXHJcbiAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG59KVxyXG5cclxucmVsaW5rLmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoXHJcbiAgZnVuY3Rpb24gKHJlcykge1xyXG4gICAgcmV0dXJuIHJlcy5kYXRhXHJcbiAgfSxcclxuICBmdW5jdGlvbiAoZXJyb3IpIHtcclxuICAgIGlmIChlcnJvci5yZXNwb25zZS5zdGF0dXMgIT09IDQwMSkge1xyXG4gICAgICB0b2FzdC5lcnJvcignTmV0d29yayBlcnJvcicpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXHJcbiAgfVxyXG4pXHJcblxyXG5leHBvcnQgZGVmYXVsdCByZWxpbmtcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwidG9hc3QiLCJyZWxpbmsiLCJjcmVhdGUiLCJiYXNlVVJMIiwid2l0aENyZWRlbnRpYWxzIiwiaW50ZXJjZXB0b3JzIiwicmVzcG9uc2UiLCJ1c2UiLCJyZXMiLCJkYXRhIiwiZXJyb3IiLCJzdGF0dXMiLCJQcm9taXNlIiwicmVqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/connect.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/UserProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/UserProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/connect */ \"(ssr)/./src/connect.ts\");\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider({ children }) {\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshUser = async ()=>{\n        try {\n            const res = await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/user\");\n            if (res.data && res.data.providerAccountId) {\n                setUserData(res.data);\n            } else {\n                setUserData(undefined);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user:\", error);\n            setUserData(undefined);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/auth/logout\");\n            setUserData(undefined);\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n            // 即使API调用失败，也清除本地状态\n            setUserData(undefined);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUser = async ()=>{\n            setLoading(true);\n            await refreshUser();\n            setLoading(false);\n        };\n        fetchUser();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            userData,\n            setUserData,\n            loading,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\providers\\\\UserProvider.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction useUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/UserProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/toast.ts":
/*!****************************!*\
  !*** ./src/utils/toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Simple toast implementation for React\nlet toastContainer = null;\nconst createToastContainer = ()=>{\n    if (!toastContainer) {\n        toastContainer = document.createElement(\"div\");\n        toastContainer.className = \"fixed top-4 right-4 z-50 space-y-2\";\n        document.body.appendChild(toastContainer);\n    }\n    return toastContainer;\n};\nconst showToast = (type, text, duration)=>{\n    const container = createToastContainer();\n    const toastElement = document.createElement(\"div\");\n    toastElement.className = `alert alert-${type} shadow-lg max-w-sm`;\n    toastElement.innerHTML = `\r\n    <div>\r\n      <span>${text}</span>\r\n    </div>\r\n  `;\n    container.appendChild(toastElement);\n    // Animate in\n    toastElement.style.opacity = \"0\";\n    toastElement.style.transform = \"translateX(100%)\";\n    setTimeout(()=>{\n        toastElement.style.transition = \"all 0.3s ease\";\n        toastElement.style.opacity = \"1\";\n        toastElement.style.transform = \"translateX(0)\";\n    }, 10);\n    // Remove after duration\n    setTimeout(()=>{\n        toastElement.style.opacity = \"0\";\n        toastElement.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (container.contains(toastElement)) {\n                container.removeChild(toastElement);\n            }\n        }, 300);\n    }, duration);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    warning: (text, duration = 2000)=>{\n        showToast(\"warning\", text, duration);\n    },\n    success: (text, duration = 2000)=>{\n        showToast(\"success\", text, duration);\n    },\n    error: (text, duration = 2000)=>{\n        showToast(\"error\", text, duration);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/toast.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7a4ab0f154ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2E5MmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3YTRhYjBmMTU0ZWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _components_NavBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NavBar */ \"(rsc)/./src/components/NavBar.tsx\");\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/UserProvider */ \"(rsc)/./src/providers/UserProvider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Mind Elixir Cloud\",\n    description: \"A powerful mind mapping application\"\n};\nasync function RootLayout({ children, params: { locale } }) {\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__.UserProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 w-screen z-10 my-5 pointer-events-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-3/5 m-auto rounded drop-shadow pointer-events-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QjtBQUMyQjtBQUNKO0FBQ047QUFDZTtBQUVoRCxNQUFNSSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLGVBQWVDLFdBQVcsRUFDdkNDLFFBQVEsRUFDUkMsUUFBUSxFQUFFQyxNQUFNLEVBQUUsRUFJbkI7SUFDQyxNQUFNQyxXQUFXLE1BQU1WLDREQUFXQTtJQUVsQyxxQkFDRSw4REFBQ1c7UUFBS0MsTUFBTUg7a0JBQ1YsNEVBQUNJO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNmLGlEQUFzQkE7Z0JBQUNXLFVBQVVBOzBCQUNoQyw0RUFBQ1IsaUVBQVlBOztzQ0FDWCw4REFBQ2E7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNiLDBEQUFNQTtnQ0FBQ2EsV0FBVTs7Ozs7Ozs7Ozs7d0JBRW5CUDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3g/ODUzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIH0gZnJvbSAnbmV4dC1pbnRsJ1xuaW1wb3J0IHsgZ2V0TWVzc2FnZXMgfSBmcm9tICduZXh0LWludGwvc2VydmVyJ1xuaW1wb3J0IE5hdkJhciBmcm9tICdAL2NvbXBvbmVudHMvTmF2QmFyJ1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9wcm92aWRlcnMvVXNlclByb3ZpZGVyJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01pbmQgRWxpeGlyIENsb3VkJyxcbiAgZGVzY3JpcHRpb246ICdBIHBvd2VyZnVsIG1pbmQgbWFwcGluZyBhcHBsaWNhdGlvbicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbiAgcGFyYW1zOiB7IGxvY2FsZSB9XG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgcGFyYW1zOiB7IGxvY2FsZTogc3RyaW5nIH1cbn0pIHtcbiAgY29uc3QgbWVzc2FnZXMgPSBhd2FpdCBnZXRNZXNzYWdlcygpXG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPXtsb2NhbGV9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIDxOZXh0SW50bENsaWVudFByb3ZpZGVyIG1lc3NhZ2VzPXttZXNzYWdlc30+XG4gICAgICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgdy1zY3JlZW4gei0xMCBteS01IHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgPE5hdkJhciBjbGFzc05hbWU9XCJ3LTMvNSBtLWF1dG8gcm91bmRlZCBkcm9wLXNoYWRvdyBwb2ludGVyLWV2ZW50cy1hdXRvXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvVXNlclByb3ZpZGVyPlxuICAgICAgICA8L05leHRJbnRsQ2xpZW50UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiTmV4dEludGxDbGllbnRQcm92aWRlciIsImdldE1lc3NhZ2VzIiwiTmF2QmFyIiwiVXNlclByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwicGFyYW1zIiwibG9jYWxlIiwibWVzc2FnZXMiLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/list/[type]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/[locale]/list/[type]/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\app\[locale]\list\[type]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst metadata = {\n    title: \"Mind Elixir Cloud\",\n    description: \"A powerful mind mapping application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFTyxNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDRUg7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIk1pbmQgRWxpeGlyIENsb3VkXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkEgcG93ZXJmdWwgbWluZCBtYXBwaW5nIGFwcGxpY2F0aW9uXCIsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/NavBar.tsx":
/*!***********************************!*\
  !*** ./src/components/NavBar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\components\NavBar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || ![\n        \"en\",\n        \"cn\",\n        \"ja\"\n    ].includes(locale)) {\n        locale = \"en\";\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUVuRCxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBRUMsYUFBYSxFQUFFO0lBQ3RELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQztRQUFDO1FBQU07UUFBTTtLQUFLLENBQUNDLFFBQVEsQ0FBQ0QsU0FBUztRQUNuREEsU0FBUztJQUNYO0lBRUEsT0FBTztRQUNMQTtRQUNBRSxVQUFVLENBQUMsTUFBTSx5RUFBTyxHQUFhLEVBQUVGLE9BQU8sTUFBTSxHQUFHRyxPQUFPO0lBQ2hFO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vc3JjL2kxOG4udHM/YmNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcidcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGdldFJlcXVlc3RDb25maWcoYXN5bmMgKHsgcmVxdWVzdExvY2FsZSB9KSA9PiB7XHJcbiAgLy8gVGhpcyB0eXBpY2FsbHkgY29ycmVzcG9uZHMgdG8gdGhlIGBbbG9jYWxlXWAgc2VnbWVudFxyXG4gIGxldCBsb2NhbGUgPSBhd2FpdCByZXF1ZXN0TG9jYWxlXHJcblxyXG4gIC8vIEVuc3VyZSB0aGF0IGEgdmFsaWQgbG9jYWxlIGlzIHVzZWRcclxuICBpZiAoIWxvY2FsZSB8fCAhWydlbicsICdjbicsICdqYSddLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgIGxvY2FsZSA9ICdlbidcclxuICB9XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBsb2NhbGUsXHJcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdFxyXG4gIH1cclxufSlcclxuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJyZXF1ZXN0TG9jYWxlIiwibG9jYWxlIiwiaW5jbHVkZXMiLCJtZXNzYWdlcyIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/providers/UserProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/UserProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserProvider: () => (/* binding */ e0),
/* harmony export */   useUser: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\providers\UserProvider.tsx#UserProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\providers\UserProvider.tsx#useUser`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/@formatjs+icu-messageformat-parser@2.11.2","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.7.7","vendor-chunks/use-intl@3.26.5_react@18.3.1","vendor-chunks/intl-messageformat@10.7.16","vendor-chunks/tslib@2.8.1","vendor-chunks/@formatjs+icu-skeleton-parser@1.8.14","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.3.7","vendor-chunks/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1","vendor-chunks/form-data@4.0.1","vendor-chunks/asynckit@0.4.0","vendor-chunks/@formatjs+fast-memoize@2.2.7","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/has-flag@4.0.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&page=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Flist%2F%5Btype%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();