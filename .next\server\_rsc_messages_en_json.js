"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_en_json";
exports.ids = ["_rsc_messages_en_json"];
exports.modules = {

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"misc":{"pleaseLogin":"Please login first","networkError":"Network error","formatSelect":"Choose format","title":"Title","ok":"OK","cancel":"Cancel","login":"Login","share":"Share","loggingIn":"Logging in..."},"menu":{"public":"Public","folder":"Folder","about":"About","language":"language"},"button":{"signin":"Sign in","signinWithWeibo":"Sign in with Weibo","signinWithGitHub":"Sign in with GitHub","logout":"Sign out","new":"New","save":"Save"},"title":{"signinWith":"Sign in with"},"about":{"1":"Thank you for the free service provided by Netify and Fly.io.","2":"Regularly Data Backup is recommended.","3":"Should you require customization features, please reach <NAME_EMAIL>."}}');

/***/ })

};
;