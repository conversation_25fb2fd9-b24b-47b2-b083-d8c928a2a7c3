interface PaginationProps {
  page: number
  pageSize: number
  total: number
  onPageChange: (page: number) => void
}

export default function Pagination({
  page,
  pageSize,
  total,
  onPageChange,
}: PaginationProps) {
  const totalPages = Math.ceil(total / pageSize)

  if (totalPages <= 1) return null

  const getVisiblePages = () => {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (
      let i = Math.max(2, page - delta);
      i <= Math.min(totalPages - 1, page + delta);
      i++
    ) {
      range.push(i)
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...')
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (page + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages)
    } else {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  return (
    <div className="join">
      <button
        className="join-item btn"
        onClick={() => onPageChange(page - 1)}
        disabled={page <= 1}
      >
        «
      </button>
      
      {getVisiblePages().map((pageNum, index) => (
        <button
          key={index}
          className={`join-item btn ${
            pageNum === page ? 'btn-active' : ''
          } ${pageNum === '...' ? 'btn-disabled' : ''}`}
          onClick={() => typeof pageNum === 'number' && onPageChange(pageNum)}
          disabled={pageNum === '...'}
        >
          {pageNum}
        </button>
      ))}
      
      <button
        className="join-item btn"
        onClick={() => onPageChange(page + 1)}
        disabled={page >= totalPages}
      >
        »
      </button>
    </div>
  )
}
