/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_MindElixirReact_tsx"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js ***!
  \**********************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("/*! @license DOMPurify 3.1.7 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.1.7/LICENSE */\n\n(function (global, factory) {\n   true ? module.exports = factory() :\n  0;\n})(this, (function () { 'use strict';\n\n  const {\n    entries,\n    setPrototypeOf,\n    isFrozen,\n    getPrototypeOf,\n    getOwnPropertyDescriptor\n  } = Object;\n  let {\n    freeze,\n    seal,\n    create\n  } = Object; // eslint-disable-line import/no-mutable-exports\n  let {\n    apply,\n    construct\n  } = typeof Reflect !== 'undefined' && Reflect;\n  if (!freeze) {\n    freeze = function freeze(x) {\n      return x;\n    };\n  }\n  if (!seal) {\n    seal = function seal(x) {\n      return x;\n    };\n  }\n  if (!apply) {\n    apply = function apply(fun, thisValue, args) {\n      return fun.apply(thisValue, args);\n    };\n  }\n  if (!construct) {\n    construct = function construct(Func, args) {\n      return new Func(...args);\n    };\n  }\n  const arrayForEach = unapply(Array.prototype.forEach);\n  const arrayPop = unapply(Array.prototype.pop);\n  const arrayPush = unapply(Array.prototype.push);\n  const stringToLowerCase = unapply(String.prototype.toLowerCase);\n  const stringToString = unapply(String.prototype.toString);\n  const stringMatch = unapply(String.prototype.match);\n  const stringReplace = unapply(String.prototype.replace);\n  const stringIndexOf = unapply(String.prototype.indexOf);\n  const stringTrim = unapply(String.prototype.trim);\n  const objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n  const regExpTest = unapply(RegExp.prototype.test);\n  const typeErrorCreate = unconstruct(TypeError);\n\n  /**\n   * Creates a new function that calls the given function with a specified thisArg and arguments.\n   *\n   * @param {Function} func - The function to be wrapped and called.\n   * @returns {Function} A new function that calls the given function with a specified thisArg and arguments.\n   */\n  function unapply(func) {\n    return function (thisArg) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      return apply(func, thisArg, args);\n    };\n  }\n\n  /**\n   * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n   *\n   * @param {Function} func - The constructor function to be wrapped and called.\n   * @returns {Function} A new function that constructs an instance of the given constructor function with the provided arguments.\n   */\n  function unconstruct(func) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return construct(func, args);\n    };\n  }\n\n  /**\n   * Add properties to a lookup table\n   *\n   * @param {Object} set - The set to which elements will be added.\n   * @param {Array} array - The array containing elements to be added to the set.\n   * @param {Function} transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n   * @returns {Object} The modified set with added elements.\n   */\n  function addToSet(set, array) {\n    let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n    if (setPrototypeOf) {\n      // Make 'in' and truthy checks like Boolean(set.constructor)\n      // independent of any properties defined on Object.prototype.\n      // Prevent prototype setters from intercepting set as a this value.\n      setPrototypeOf(set, null);\n    }\n    let l = array.length;\n    while (l--) {\n      let element = array[l];\n      if (typeof element === 'string') {\n        const lcElement = transformCaseFunc(element);\n        if (lcElement !== element) {\n          // Config presets (e.g. tags.js, attrs.js) are immutable.\n          if (!isFrozen(array)) {\n            array[l] = lcElement;\n          }\n          element = lcElement;\n        }\n      }\n      set[element] = true;\n    }\n    return set;\n  }\n\n  /**\n   * Clean up an array to harden against CSPP\n   *\n   * @param {Array} array - The array to be cleaned.\n   * @returns {Array} The cleaned version of the array\n   */\n  function cleanArray(array) {\n    for (let index = 0; index < array.length; index++) {\n      const isPropertyExist = objectHasOwnProperty(array, index);\n      if (!isPropertyExist) {\n        array[index] = null;\n      }\n    }\n    return array;\n  }\n\n  /**\n   * Shallow clone an object\n   *\n   * @param {Object} object - The object to be cloned.\n   * @returns {Object} A new object that copies the original.\n   */\n  function clone(object) {\n    const newObject = create(null);\n    for (const [property, value] of entries(object)) {\n      const isPropertyExist = objectHasOwnProperty(object, property);\n      if (isPropertyExist) {\n        if (Array.isArray(value)) {\n          newObject[property] = cleanArray(value);\n        } else if (value && typeof value === 'object' && value.constructor === Object) {\n          newObject[property] = clone(value);\n        } else {\n          newObject[property] = value;\n        }\n      }\n    }\n    return newObject;\n  }\n\n  /**\n   * This method automatically checks if the prop is function or getter and behaves accordingly.\n   *\n   * @param {Object} object - The object to look up the getter function in its prototype chain.\n   * @param {String} prop - The property name for which to find the getter function.\n   * @returns {Function} The getter function found in the prototype chain or a fallback function.\n   */\n  function lookupGetter(object, prop) {\n    while (object !== null) {\n      const desc = getOwnPropertyDescriptor(object, prop);\n      if (desc) {\n        if (desc.get) {\n          return unapply(desc.get);\n        }\n        if (typeof desc.value === 'function') {\n          return unapply(desc.value);\n        }\n      }\n      object = getPrototypeOf(object);\n    }\n    function fallbackValue() {\n      return null;\n    }\n    return fallbackValue;\n  }\n\n  const html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\n\n  // SVG\n  const svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\n  const svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n\n  // List of SVG elements that are disallowed by default.\n  // We still need to know them so that we can do namespace\n  // checks properly in case one wants to add them to\n  // allow-list.\n  const svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\n  const mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n\n  // Similarly to SVG, we want to know all MathML elements,\n  // even those that we disallow by default.\n  const mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\n  const text = freeze(['#text']);\n\n  const html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\n  const svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\n  const mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\n  const xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n  // eslint-disable-next-line unicorn/better-regex\n  const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n  const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\n  const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\n  const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n  const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n  const IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n  );\n  const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\n  const ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n  );\n  const DOCTYPE_NAME = seal(/^html$/i);\n  const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n\n  var EXPRESSIONS = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    MUSTACHE_EXPR: MUSTACHE_EXPR,\n    ERB_EXPR: ERB_EXPR,\n    TMPLIT_EXPR: TMPLIT_EXPR,\n    DATA_ATTR: DATA_ATTR,\n    ARIA_ATTR: ARIA_ATTR,\n    IS_ALLOWED_URI: IS_ALLOWED_URI,\n    IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE: ATTR_WHITESPACE,\n    DOCTYPE_NAME: DOCTYPE_NAME,\n    CUSTOM_ELEMENT: CUSTOM_ELEMENT\n  });\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n  const NODE_TYPE = {\n    element: 1,\n    attribute: 2,\n    text: 3,\n    cdataSection: 4,\n    entityReference: 5,\n    // Deprecated\n    entityNode: 6,\n    // Deprecated\n    progressingInstruction: 7,\n    comment: 8,\n    document: 9,\n    documentType: 10,\n    documentFragment: 11,\n    notation: 12 // Deprecated\n  };\n  const getGlobal = function getGlobal() {\n    return typeof window === 'undefined' ? null : window;\n  };\n\n  /**\n   * Creates a no-op policy for internal use only.\n   * Don't export this function outside this module!\n   * @param {TrustedTypePolicyFactory} trustedTypes The policy factory.\n   * @param {HTMLScriptElement} purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n   * @return {TrustedTypePolicy} The policy created (or null, if Trusted Types\n   * are not supported or creating the policy failed).\n   */\n  const _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n    if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n      return null;\n    }\n\n    // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n    let suffix = null;\n    const ATTR_NAME = 'data-tt-policy-suffix';\n    if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n      suffix = purifyHostElement.getAttribute(ATTR_NAME);\n    }\n    const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n    try {\n      return trustedTypes.createPolicy(policyName, {\n        createHTML(html) {\n          return html;\n        },\n        createScriptURL(scriptUrl) {\n          return scriptUrl;\n        }\n      });\n    } catch (_) {\n      // Policy creation failed (most likely another DOMPurify script has\n      // already run). Skip creating the policy, as this will only cause errors\n      // if TT are enforced.\n      console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n      return null;\n    }\n  };\n  function createDOMPurify() {\n    let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n    const DOMPurify = root => createDOMPurify(root);\n\n    /**\n     * Version label, exposed for easier checks\n     * if DOMPurify is up to date or not\n     */\n    DOMPurify.version = '3.1.7';\n\n    /**\n     * Array of elements that DOMPurify removed during sanitation.\n     * Empty if nothing was removed.\n     */\n    DOMPurify.removed = [];\n    if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document) {\n      // Not running in a browser, provide a factory function\n      // so that you can pass your own Window\n      DOMPurify.isSupported = false;\n      return DOMPurify;\n    }\n    let {\n      document\n    } = window;\n    const originalDocument = document;\n    const currentScript = originalDocument.currentScript;\n    const {\n      DocumentFragment,\n      HTMLTemplateElement,\n      Node,\n      Element,\n      NodeFilter,\n      NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n      HTMLFormElement,\n      DOMParser,\n      trustedTypes\n    } = window;\n    const ElementPrototype = Element.prototype;\n    const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n    const remove = lookupGetter(ElementPrototype, 'remove');\n    const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n    const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n    const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n    // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n    if (typeof HTMLTemplateElement === 'function') {\n      const template = document.createElement('template');\n      if (template.content && template.content.ownerDocument) {\n        document = template.content.ownerDocument;\n      }\n    }\n    let trustedTypesPolicy;\n    let emptyHTML = '';\n    const {\n      implementation,\n      createNodeIterator,\n      createDocumentFragment,\n      getElementsByTagName\n    } = document;\n    const {\n      importNode\n    } = originalDocument;\n    let hooks = {};\n\n    /**\n     * Expose whether this browser supports running the full DOMPurify.\n     */\n    DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n    const {\n      MUSTACHE_EXPR,\n      ERB_EXPR,\n      TMPLIT_EXPR,\n      DATA_ATTR,\n      ARIA_ATTR,\n      IS_SCRIPT_OR_DATA,\n      ATTR_WHITESPACE,\n      CUSTOM_ELEMENT\n    } = EXPRESSIONS;\n    let {\n      IS_ALLOWED_URI: IS_ALLOWED_URI$1\n    } = EXPRESSIONS;\n\n    /**\n     * We consider the elements and attributes below to be safe. Ideally\n     * don't add any new ones but feel free to remove unwanted ones.\n     */\n\n    /* allowed element names */\n    let ALLOWED_TAGS = null;\n    const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n\n    /* Allowed attribute names */\n    let ALLOWED_ATTR = null;\n    const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n\n    /*\n     * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n     * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n     * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n     * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n     */\n    let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false\n      }\n    }));\n\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n    let FORBID_TAGS = null;\n\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n    let FORBID_ATTR = null;\n\n    /* Decide if ARIA attributes are okay */\n    let ALLOW_ARIA_ATTR = true;\n\n    /* Decide if custom data attributes are okay */\n    let ALLOW_DATA_ATTR = true;\n\n    /* Decide if unknown protocols are okay */\n    let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n    /* Decide if self-closing tags in attributes are allowed.\n     * Usually removed due to a mXSS issue in jQuery 3.0 */\n    let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n    /* Output should be safe for common template engines.\n     * This means, DOMPurify removes data attributes, mustaches and ERB\n     */\n    let SAFE_FOR_TEMPLATES = false;\n\n    /* Output should be safe even for XML used within HTML and alike.\n     * This means, DOMPurify removes comments when containing risky content.\n     */\n    let SAFE_FOR_XML = true;\n\n    /* Decide if document with <html>... should be returned */\n    let WHOLE_DOCUMENT = false;\n\n    /* Track whether config is already set on this instance of DOMPurify. */\n    let SET_CONFIG = false;\n\n    /* Decide if all elements (e.g. style, script) must be children of\n     * document.body. By default, browsers might move them to document.head */\n    let FORCE_BODY = false;\n\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n     * string (or a TrustedHTML object if Trusted Types are supported).\n     * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n     */\n    let RETURN_DOM = false;\n\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n     * string  (or a TrustedHTML object if Trusted Types are supported) */\n    let RETURN_DOM_FRAGMENT = false;\n\n    /* Try to return a Trusted Type object instead of a string, return a string in\n     * case Trusted Types are not supported  */\n    let RETURN_TRUSTED_TYPE = false;\n\n    /* Output should be free from DOM clobbering attacks?\n     * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n     */\n    let SANITIZE_DOM = true;\n\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n     * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n     *\n     * HTML/DOM spec rules that enable DOM Clobbering:\n     *   - Named Access on Window (§7.3.3)\n     *   - DOM Tree Accessors (§3.1.5)\n     *   - Form Element Parent-Child Relations (§4.10.3)\n     *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n     *   - HTMLCollection (§4.2.10.2)\n     *\n     * Namespace isolation is implemented by prefixing `id` and `name` attributes\n     * with a constant string, i.e., `user-content-`\n     */\n    let SANITIZE_NAMED_PROPS = false;\n    const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n    /* Keep element content when removing element? */\n    let KEEP_CONTENT = true;\n\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n     * of importing it into a new Document and returning a sanitized copy */\n    let IN_PLACE = false;\n\n    /* Allow usage of profiles like html, svg and mathMl */\n    let USE_PROFILES = {};\n\n    /* Tags to ignore content of when KEEP_CONTENT is true */\n    let FORBID_CONTENTS = null;\n    const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n\n    /* Tags that are safe for data: URIs */\n    let DATA_URI_TAGS = null;\n    const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n\n    /* Attributes safe for values like \"javascript:\" */\n    let URI_SAFE_ATTRIBUTES = null;\n    const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n    const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n    const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n    const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n    /* Document namespace */\n    let NAMESPACE = HTML_NAMESPACE;\n    let IS_EMPTY_INPUT = false;\n\n    /* Allowed XHTML+XML namespaces */\n    let ALLOWED_NAMESPACES = null;\n    const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n\n    /* Parsing of strict XHTML documents */\n    let PARSER_MEDIA_TYPE = null;\n    const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n    const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n    let transformCaseFunc = null;\n\n    /* Keep a reference to config to pass to hooks */\n    let CONFIG = null;\n\n    /* Ideally, do not touch anything below this line */\n    /* ______________________________________________ */\n\n    const formElement = document.createElement('form');\n    const isRegexOrFunction = function isRegexOrFunction(testValue) {\n      return testValue instanceof RegExp || testValue instanceof Function;\n    };\n\n    /**\n     * _parseConfig\n     *\n     * @param  {Object} cfg optional config literal\n     */\n    // eslint-disable-next-line complexity\n    const _parseConfig = function _parseConfig() {\n      let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (CONFIG && CONFIG === cfg) {\n        return;\n      }\n\n      /* Shield configuration object from tampering */\n      if (!cfg || typeof cfg !== 'object') {\n        cfg = {};\n      }\n\n      /* Shield configuration object from prototype pollution */\n      cfg = clone(cfg);\n      PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n\n      // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n      transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n\n      /* Set configuration parameters */\n      ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n      ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n      ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n      URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n      // eslint-disable-line indent\n      cfg.ADD_URI_SAFE_ATTR,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n      DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS),\n      // eslint-disable-line indent\n      cfg.ADD_DATA_URI_TAGS,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_DATA_URI_TAGS;\n      FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n      FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n      FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n      USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n      ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n      ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n      ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n      ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n      SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n      SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n      WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n      RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n      RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n      RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n      FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n      SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n      SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n      KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n      IN_PLACE = cfg.IN_PLACE || false; // Default false\n      IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n      NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n      CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n        CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n      }\n      if (SAFE_FOR_TEMPLATES) {\n        ALLOW_DATA_ATTR = false;\n      }\n      if (RETURN_DOM_FRAGMENT) {\n        RETURN_DOM = true;\n      }\n\n      /* Parse profile info */\n      if (USE_PROFILES) {\n        ALLOWED_TAGS = addToSet({}, text);\n        ALLOWED_ATTR = [];\n        if (USE_PROFILES.html === true) {\n          addToSet(ALLOWED_TAGS, html$1);\n          addToSet(ALLOWED_ATTR, html);\n        }\n        if (USE_PROFILES.svg === true) {\n          addToSet(ALLOWED_TAGS, svg$1);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.svgFilters === true) {\n          addToSet(ALLOWED_TAGS, svgFilters);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.mathMl === true) {\n          addToSet(ALLOWED_TAGS, mathMl$1);\n          addToSet(ALLOWED_ATTR, mathMl);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n      }\n\n      /* Merge configuration parameters */\n      if (cfg.ADD_TAGS) {\n        if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n          ALLOWED_TAGS = clone(ALLOWED_TAGS);\n        }\n        addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n      }\n      if (cfg.ADD_ATTR) {\n        if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n          ALLOWED_ATTR = clone(ALLOWED_ATTR);\n        }\n        addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n      }\n      if (cfg.ADD_URI_SAFE_ATTR) {\n        addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n      }\n      if (cfg.FORBID_CONTENTS) {\n        if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n          FORBID_CONTENTS = clone(FORBID_CONTENTS);\n        }\n        addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n      }\n\n      /* Add #text in case KEEP_CONTENT is set to true */\n      if (KEEP_CONTENT) {\n        ALLOWED_TAGS['#text'] = true;\n      }\n\n      /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n      if (WHOLE_DOCUMENT) {\n        addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n      }\n\n      /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n      if (ALLOWED_TAGS.table) {\n        addToSet(ALLOWED_TAGS, ['tbody']);\n        delete FORBID_TAGS.tbody;\n      }\n      if (cfg.TRUSTED_TYPES_POLICY) {\n        if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n          throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n        }\n        if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n          throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n        }\n\n        // Overwrite existing TrustedTypes policy.\n        trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n        // Sign local variables required by `sanitize`.\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      } else {\n        // Uninitialized policy, attempt to initialize the internal dompurify policy.\n        if (trustedTypesPolicy === undefined) {\n          trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n        }\n\n        // If creating the internal policy succeeded sign internal variables.\n        if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n          emptyHTML = trustedTypesPolicy.createHTML('');\n        }\n      }\n\n      // Prevent further manipulation of configuration.\n      // Not available in IE8, Safari 5, etc.\n      if (freeze) {\n        freeze(cfg);\n      }\n      CONFIG = cfg;\n    };\n    const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n    const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n    // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n    const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n\n    /* Keep track of all possible SVG and MathML tags\n     * so that we can perform the namespace checks\n     * correctly. */\n    const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n    const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n\n    /**\n     * @param  {Element} element a DOM element whose namespace is being checked\n     * @returns {boolean} Return false if the element has a\n     *  namespace that a spec-compliant parser would never\n     *  return. Return true otherwise.\n     */\n    const _checkValidNamespace = function _checkValidNamespace(element) {\n      let parent = getParentNode(element);\n\n      // In JSDOM, if we're inside shadow DOM, then parentNode\n      // can be null. We just simulate parent in this case.\n      if (!parent || !parent.tagName) {\n        parent = {\n          namespaceURI: NAMESPACE,\n          tagName: 'template'\n        };\n      }\n      const tagName = stringToLowerCase(element.tagName);\n      const parentTagName = stringToLowerCase(parent.tagName);\n      if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return false;\n      }\n      if (element.namespaceURI === SVG_NAMESPACE) {\n        // The only way to switch from HTML namespace to SVG\n        // is via <svg>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'svg';\n        }\n\n        // The only way to switch from MathML to SVG is via`\n        // svg if parent is either <annotation-xml> or MathML\n        // text integration points.\n        if (parent.namespaceURI === MATHML_NAMESPACE) {\n          return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n        }\n\n        // We only allow elements that are defined in SVG\n        // spec. All others are disallowed in SVG namespace.\n        return Boolean(ALL_SVG_TAGS[tagName]);\n      }\n      if (element.namespaceURI === MATHML_NAMESPACE) {\n        // The only way to switch from HTML namespace to MathML\n        // is via <math>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'math';\n        }\n\n        // The only way to switch from SVG to MathML is via\n        // <math> and HTML integration points\n        if (parent.namespaceURI === SVG_NAMESPACE) {\n          return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n        }\n\n        // We only allow elements that are defined in MathML\n        // spec. All others are disallowed in MathML namespace.\n        return Boolean(ALL_MATHML_TAGS[tagName]);\n      }\n      if (element.namespaceURI === HTML_NAMESPACE) {\n        // The only way to switch from SVG to HTML is via\n        // HTML integration points, and from MathML to HTML\n        // is via MathML text integration points\n        if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n        if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n\n        // We disallow tags that are specific for MathML\n        // or SVG and should never appear in HTML namespace\n        return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n      }\n\n      // For XHTML and XML documents that support custom namespaces\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return true;\n      }\n\n      // The code should never reach this place (this means\n      // that the element somehow got namespace that is not\n      // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n      // Return false just in case.\n      return false;\n    };\n\n    /**\n     * _forceRemove\n     *\n     * @param  {Node} node a DOM node\n     */\n    const _forceRemove = function _forceRemove(node) {\n      arrayPush(DOMPurify.removed, {\n        element: node\n      });\n      try {\n        // eslint-disable-next-line unicorn/prefer-dom-node-remove\n        getParentNode(node).removeChild(node);\n      } catch (_) {\n        remove(node);\n      }\n    };\n\n    /**\n     * _removeAttribute\n     *\n     * @param  {String} name an Attribute name\n     * @param  {Node} node a DOM node\n     */\n    const _removeAttribute = function _removeAttribute(name, node) {\n      try {\n        arrayPush(DOMPurify.removed, {\n          attribute: node.getAttributeNode(name),\n          from: node\n        });\n      } catch (_) {\n        arrayPush(DOMPurify.removed, {\n          attribute: null,\n          from: node\n        });\n      }\n      node.removeAttribute(name);\n\n      // We void attribute values for unremovable \"is\"\" attributes\n      if (name === 'is' && !ALLOWED_ATTR[name]) {\n        if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n          try {\n            _forceRemove(node);\n          } catch (_) {}\n        } else {\n          try {\n            node.setAttribute(name, '');\n          } catch (_) {}\n        }\n      }\n    };\n\n    /**\n     * _initDocument\n     *\n     * @param  {String} dirty a string of dirty markup\n     * @return {Document} a DOM, filled with the dirty markup\n     */\n    const _initDocument = function _initDocument(dirty) {\n      /* Create a HTML document */\n      let doc = null;\n      let leadingWhitespace = null;\n      if (FORCE_BODY) {\n        dirty = '<remove></remove>' + dirty;\n      } else {\n        /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n        const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n        leadingWhitespace = matches && matches[0];\n      }\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n        // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n        dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n      }\n      const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      /*\n       * Use the DOMParser API by default, fallback later if needs be\n       * DOMParser not work for svg when has multiple root element.\n       */\n      if (NAMESPACE === HTML_NAMESPACE) {\n        try {\n          doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n        } catch (_) {}\n      }\n\n      /* Use createHTMLDocument in case DOMParser is not available */\n      if (!doc || !doc.documentElement) {\n        doc = implementation.createDocument(NAMESPACE, 'template', null);\n        try {\n          doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n        } catch (_) {\n          // Syntax error if dirtyPayload is invalid xml\n        }\n      }\n      const body = doc.body || doc.documentElement;\n      if (dirty && leadingWhitespace) {\n        body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n      }\n\n      /* Work on whole document or just its body */\n      if (NAMESPACE === HTML_NAMESPACE) {\n        return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n      }\n      return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n\n    /**\n     * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n     *\n     * @param  {Node} root The root element or node to start traversing on.\n     * @return {NodeIterator} The created NodeIterator\n     */\n    const _createNodeIterator = function _createNodeIterator(root) {\n      return createNodeIterator.call(root.ownerDocument || root, root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n    };\n\n    /**\n     * _isClobbered\n     *\n     * @param  {Node} elm element to check for clobbering attacks\n     * @return {Boolean} true if clobbered, false if safe\n     */\n    const _isClobbered = function _isClobbered(elm) {\n      return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n    };\n\n    /**\n     * Checks whether the given object is a DOM node.\n     *\n     * @param  {Node} object object to check whether it's a DOM node\n     * @return {Boolean} true is object is a DOM node\n     */\n    const _isNode = function _isNode(object) {\n      return typeof Node === 'function' && object instanceof Node;\n    };\n\n    /**\n     * _executeHook\n     * Execute user configurable hooks\n     *\n     * @param  {String} entryPoint  Name of the hook's entry point\n     * @param  {Node} currentNode node to work on with the hook\n     * @param  {Object} data additional hook parameters\n     */\n    const _executeHook = function _executeHook(entryPoint, currentNode, data) {\n      if (!hooks[entryPoint]) {\n        return;\n      }\n      arrayForEach(hooks[entryPoint], hook => {\n        hook.call(DOMPurify, currentNode, data, CONFIG);\n      });\n    };\n\n    /**\n     * _sanitizeElements\n     *\n     * @protect nodeName\n     * @protect textContent\n     * @protect removeChild\n     *\n     * @param   {Node} currentNode to check for permission to exist\n     * @return  {Boolean} true if node was killed, false if left alive\n     */\n    const _sanitizeElements = function _sanitizeElements(currentNode) {\n      let content = null;\n\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeElements', currentNode, null);\n\n      /* Check if element is clobbered or can clobber */\n      if (_isClobbered(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Now let's check the element's type and name */\n      const tagName = transformCaseFunc(currentNode.nodeName);\n\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeElement', currentNode, {\n        tagName,\n        allowedTags: ALLOWED_TAGS\n      });\n\n      /* Detect mXSS attempts abusing namespace confusion */\n      if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove any occurrence of processing instructions */\n      if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove any kind of possibly harmful comments */\n      if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Remove element if anything forbids its presence */\n      if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n        /* Check if we have a custom element to handle */\n        if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n            return false;\n          }\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n            return false;\n          }\n        }\n\n        /* Keep content except for bad-listed elements */\n        if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n          const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n          const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n          if (childNodes && parentNode) {\n            const childCount = childNodes.length;\n            for (let i = childCount - 1; i >= 0; --i) {\n              const childClone = cloneNode(childNodes[i], true);\n              childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n              parentNode.insertBefore(childClone, getNextSibling(currentNode));\n            }\n          }\n        }\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Check whether element has a valid namespace */\n      if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Make sure that older browsers don't get fallback-tag mXSS */\n      if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n\n      /* Sanitize element content to be template-safe */\n      if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n        /* Get the element's text content */\n        content = currentNode.textContent;\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          content = stringReplace(content, expr, ' ');\n        });\n        if (currentNode.textContent !== content) {\n          arrayPush(DOMPurify.removed, {\n            element: currentNode.cloneNode()\n          });\n          currentNode.textContent = content;\n        }\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeElements', currentNode, null);\n      return false;\n    };\n\n    /**\n     * _isValidAttribute\n     *\n     * @param  {string} lcTag Lowercase tag name of containing element.\n     * @param  {string} lcName Lowercase attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid, otherwise false.\n     */\n    // eslint-disable-next-line complexity\n    const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n      /* Make sure attribute cannot clobber */\n      if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n        return false;\n      }\n\n      /* Allow valid data-* attributes: At least one character after \"-\"\n          (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n          XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n          We don't need to check the value; it's always URI safe. */\n      if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n        if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n          return false;\n        }\n        /* Check value is safe. First, is attr inert? If so, is safe */\n      } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ; else if (value) {\n        return false;\n      } else ;\n      return true;\n    };\n\n    /**\n     * _isBasicCustomElement\n     * checks if at least one dash is included in tagName, and it's not the first char\n     * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n     *\n     * @param {string} tagName name of the tag of the node to sanitize\n     * @returns {boolean} Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n     */\n    const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n      return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n    };\n\n    /**\n     * _sanitizeAttributes\n     *\n     * @protect attributes\n     * @protect nodeName\n     * @protect removeAttribute\n     * @protect setAttribute\n     *\n     * @param  {Node} currentNode to sanitize\n     */\n    const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeAttributes', currentNode, null);\n      const {\n        attributes\n      } = currentNode;\n\n      /* Check if we have attributes; if not we might have a text node */\n      if (!attributes) {\n        return;\n      }\n      const hookEvent = {\n        attrName: '',\n        attrValue: '',\n        keepAttr: true,\n        allowedAttributes: ALLOWED_ATTR\n      };\n      let l = attributes.length;\n\n      /* Go backwards over all attributes; safely remove bad ones */\n      while (l--) {\n        const attr = attributes[l];\n        const {\n          name,\n          namespaceURI,\n          value: attrValue\n        } = attr;\n        const lcName = transformCaseFunc(name);\n        let value = name === 'value' ? attrValue : stringTrim(attrValue);\n\n        /* Execute a hook if present */\n        hookEvent.attrName = lcName;\n        hookEvent.attrValue = value;\n        hookEvent.keepAttr = true;\n        hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n        _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n        value = hookEvent.attrValue;\n\n        /* Did the hooks approve of the attribute? */\n        if (hookEvent.forceKeepAttr) {\n          continue;\n        }\n\n        /* Remove attribute */\n        _removeAttribute(name, currentNode);\n\n        /* Did the hooks approve of the attribute? */\n        if (!hookEvent.keepAttr) {\n          continue;\n        }\n\n        /* Work around a security issue in jQuery 3.0 */\n        if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n          _removeAttribute(name, currentNode);\n          continue;\n        }\n\n        /* Sanitize attribute content to be template-safe */\n        if (SAFE_FOR_TEMPLATES) {\n          arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n            value = stringReplace(value, expr, ' ');\n          });\n        }\n\n        /* Is `value` valid for this attribute? */\n        const lcTag = transformCaseFunc(currentNode.nodeName);\n        if (!_isValidAttribute(lcTag, lcName, value)) {\n          continue;\n        }\n\n        /* Full DOM Clobbering protection via namespace isolation,\n         * Prefix id and name attributes with `user-content-`\n         */\n        if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n          // Remove the attribute with this value\n          _removeAttribute(name, currentNode);\n\n          // Prefix the value and later re-create the attribute with the sanitized value\n          value = SANITIZE_NAMED_PROPS_PREFIX + value;\n        }\n\n        /* Work around a security issue with comments inside attributes */\n        if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n          _removeAttribute(name, currentNode);\n          continue;\n        }\n\n        /* Handle attributes that require Trusted Types */\n        if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n          if (namespaceURI) ; else {\n            switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n              case 'TrustedHTML':\n                {\n                  value = trustedTypesPolicy.createHTML(value);\n                  break;\n                }\n              case 'TrustedScriptURL':\n                {\n                  value = trustedTypesPolicy.createScriptURL(value);\n                  break;\n                }\n            }\n          }\n        }\n\n        /* Handle invalid data-* attribute set by try-catching it */\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {}\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeAttributes', currentNode, null);\n    };\n\n    /**\n     * _sanitizeShadowDOM\n     *\n     * @param  {DocumentFragment} fragment to iterate over recursively\n     */\n    const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n      let shadowNode = null;\n      const shadowIterator = _createNodeIterator(fragment);\n\n      /* Execute a hook if present */\n      _executeHook('beforeSanitizeShadowDOM', fragment, null);\n      while (shadowNode = shadowIterator.nextNode()) {\n        /* Execute a hook if present */\n        _executeHook('uponSanitizeShadowNode', shadowNode, null);\n\n        /* Sanitize tags and elements */\n        if (_sanitizeElements(shadowNode)) {\n          continue;\n        }\n\n        /* Deep shadow DOM detected */\n        if (shadowNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(shadowNode.content);\n        }\n\n        /* Check attributes, sanitize if necessary */\n        _sanitizeAttributes(shadowNode);\n      }\n\n      /* Execute a hook if present */\n      _executeHook('afterSanitizeShadowDOM', fragment, null);\n    };\n\n    /**\n     * Sanitize\n     * Public method providing core sanitation functionality\n     *\n     * @param {String|Node} dirty string or DOM node\n     * @param {Object} cfg object\n     */\n    // eslint-disable-next-line complexity\n    DOMPurify.sanitize = function (dirty) {\n      let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      let body = null;\n      let importedNode = null;\n      let currentNode = null;\n      let returnNode = null;\n      /* Make sure we have a string to sanitize.\n        DO NOT return early, as this will return the wrong type if\n        the user has requested a DOM object rather than a string */\n      IS_EMPTY_INPUT = !dirty;\n      if (IS_EMPTY_INPUT) {\n        dirty = '<!-->';\n      }\n\n      /* Stringify, in case dirty is an object */\n      if (typeof dirty !== 'string' && !_isNode(dirty)) {\n        if (typeof dirty.toString === 'function') {\n          dirty = dirty.toString();\n          if (typeof dirty !== 'string') {\n            throw typeErrorCreate('dirty is not a string, aborting');\n          }\n        } else {\n          throw typeErrorCreate('toString is not a function');\n        }\n      }\n\n      /* Return dirty HTML if DOMPurify cannot run */\n      if (!DOMPurify.isSupported) {\n        return dirty;\n      }\n\n      /* Assign config vars */\n      if (!SET_CONFIG) {\n        _parseConfig(cfg);\n      }\n\n      /* Clean up removed elements */\n      DOMPurify.removed = [];\n\n      /* Check if dirty is correctly typed for IN_PLACE */\n      if (typeof dirty === 'string') {\n        IN_PLACE = false;\n      }\n      if (IN_PLACE) {\n        /* Do some early pre-sanitization to avoid unsafe root nodes */\n        if (dirty.nodeName) {\n          const tagName = transformCaseFunc(dirty.nodeName);\n          if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n          }\n        }\n      } else if (dirty instanceof Node) {\n        /* If dirty is a DOM element, append to an empty document to avoid\n           elements being stripped by the parser */\n        body = _initDocument('<!---->');\n        importedNode = body.ownerDocument.importNode(dirty, true);\n        if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n          /* Node is already a body, use as is */\n          body = importedNode;\n        } else if (importedNode.nodeName === 'HTML') {\n          body = importedNode;\n        } else {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          body.appendChild(importedNode);\n        }\n      } else {\n        /* Exit directly if we have nothing to do */\n        if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1) {\n          return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        }\n\n        /* Initialize the document to work on */\n        body = _initDocument(dirty);\n\n        /* Check we have a DOM node from the data */\n        if (!body) {\n          return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n        }\n      }\n\n      /* Remove first element node (ours) if FORCE_BODY is set */\n      if (body && FORCE_BODY) {\n        _forceRemove(body.firstChild);\n      }\n\n      /* Get node iterator */\n      const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n      /* Now start iterating over the created document */\n      while (currentNode = nodeIterator.nextNode()) {\n        /* Sanitize tags and elements */\n        if (_sanitizeElements(currentNode)) {\n          continue;\n        }\n\n        /* Shadow DOM detected, sanitize it */\n        if (currentNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(currentNode.content);\n        }\n\n        /* Check attributes, sanitize if necessary */\n        _sanitizeAttributes(currentNode);\n      }\n\n      /* If we sanitized `dirty` in-place, return it. */\n      if (IN_PLACE) {\n        return dirty;\n      }\n\n      /* Return sanitized string or DOM */\n      if (RETURN_DOM) {\n        if (RETURN_DOM_FRAGMENT) {\n          returnNode = createDocumentFragment.call(body.ownerDocument);\n          while (body.firstChild) {\n            // eslint-disable-next-line unicorn/prefer-dom-node-append\n            returnNode.appendChild(body.firstChild);\n          }\n        } else {\n          returnNode = body;\n        }\n        if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n          /*\n            AdoptNode() is not used because internal state is not reset\n            (e.g. the past names map of a HTMLFormElement), this is safe\n            in theory but we would rather not risk another attack vector.\n            The state that is cloned by importNode() is explicitly defined\n            by the specs.\n          */\n          returnNode = importNode.call(originalDocument, returnNode, true);\n        }\n        return returnNode;\n      }\n      let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n      /* Serialize doctype if allowed */\n      if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n        serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n      }\n\n      /* Sanitize final string template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          serializedHTML = stringReplace(serializedHTML, expr, ' ');\n        });\n      }\n      return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n\n    /**\n     * Public method to set the configuration once\n     * setConfig\n     *\n     * @param {Object} cfg configuration object\n     */\n    DOMPurify.setConfig = function () {\n      let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      _parseConfig(cfg);\n      SET_CONFIG = true;\n    };\n\n    /**\n     * Public method to remove the configuration\n     * clearConfig\n     *\n     */\n    DOMPurify.clearConfig = function () {\n      CONFIG = null;\n      SET_CONFIG = false;\n    };\n\n    /**\n     * Public method to check if an attribute value is valid.\n     * Uses last set config, if any. Otherwise, uses config defaults.\n     * isValidAttribute\n     *\n     * @param  {String} tag Tag name of containing element.\n     * @param  {String} attr Attribute name.\n     * @param  {String} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n     */\n    DOMPurify.isValidAttribute = function (tag, attr, value) {\n      /* Initialize shared config vars if necessary. */\n      if (!CONFIG) {\n        _parseConfig({});\n      }\n      const lcTag = transformCaseFunc(tag);\n      const lcName = transformCaseFunc(attr);\n      return _isValidAttribute(lcTag, lcName, value);\n    };\n\n    /**\n     * AddHook\n     * Public method to add DOMPurify hooks\n     *\n     * @param {String} entryPoint entry point for the hook to add\n     * @param {Function} hookFunction function to execute\n     */\n    DOMPurify.addHook = function (entryPoint, hookFunction) {\n      if (typeof hookFunction !== 'function') {\n        return;\n      }\n      hooks[entryPoint] = hooks[entryPoint] || [];\n      arrayPush(hooks[entryPoint], hookFunction);\n    };\n\n    /**\n     * RemoveHook\n     * Public method to remove a DOMPurify hook at a given entryPoint\n     * (pops it from the stack of hooks if more are present)\n     *\n     * @param {String} entryPoint entry point for the hook to remove\n     * @return {Function} removed(popped) hook\n     */\n    DOMPurify.removeHook = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        return arrayPop(hooks[entryPoint]);\n      }\n    };\n\n    /**\n     * RemoveHooks\n     * Public method to remove all DOMPurify hooks at a given entryPoint\n     *\n     * @param  {String} entryPoint entry point for the hooks to remove\n     */\n    DOMPurify.removeHooks = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        hooks[entryPoint] = [];\n      }\n    };\n\n    /**\n     * RemoveAllHooks\n     * Public method to remove all DOMPurify hooks\n     */\n    DOMPurify.removeAllHooks = function () {\n      hooks = {};\n    };\n    return DOMPurify;\n  }\n  var purify = createDOMPurify();\n\n  return purify;\n\n}));\n//# sourceMappingURL=purify.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindElixirReact.tsx":
/*!********************************************!*\
  !*** ./src/components/MindElixirReact.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/.pnpm/dompurify@3.1.7/node_modules/dompurify/dist/purify.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dompurify__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var mind_elixir__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mind-elixir */ \"(app-pages-browser)/./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/MindElixir.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MindElixirReact = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { data, options, plugins, initScale, className } = param;\n    _s();\n    const mindmapEl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const meInstance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            instance: meInstance.current\n        }));\n    const sanitizeNodeData = (nodeData)=>{\n        if (!nodeData) return;\n        if (nodeData.dangerouslySetInnerHTML) {\n            nodeData.dangerouslySetInnerHTML = dompurify__WEBPACK_IMPORTED_MODULE_2___default().sanitize(nodeData.dangerouslySetInnerHTML);\n        }\n        if (nodeData.children) {\n            for (const child of nodeData.children){\n                sanitizeNodeData(child);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mindmapEl.current || \"object\" === \"undefined\") return;\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const changeTheme = (e)=>{\n            if (e.matches) {\n                var _meInstance_current;\n                (_meInstance_current = meInstance.current) === null || _meInstance_current === void 0 ? void 0 : _meInstance_current.changeTheme(mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"].DARK_THEME);\n            } else {\n                var _meInstance_current1;\n                (_meInstance_current1 = meInstance.current) === null || _meInstance_current1 === void 0 ? void 0 : _meInstance_current1.changeTheme(mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"].THEME);\n            }\n        };\n        const mergedOptions = {\n            ...options,\n            el: mindmapEl.current\n        };\n        meInstance.current = new mind_elixir__WEBPACK_IMPORTED_MODULE_3__[\"default\"](mergedOptions);\n        // Install plugins\n        if (plugins) {\n            for (const plugin of plugins){\n                meInstance.current.install(plugin);\n            }\n        }\n        // Set initial scale\n        if (initScale) {\n            meInstance.current.scaleVal = initScale;\n            meInstance.current.map.style.transform = \"scale(\".concat(initScale, \")\");\n        }\n        meInstance.current.map.style.opacity = \"0\";\n        mediaQuery.addEventListener(\"change\", changeTheme);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", changeTheme);\n        };\n    }, [\n        options,\n        plugins,\n        initScale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!data || !meInstance.current) return;\n        sanitizeNodeData(data.nodeData);\n        meInstance.current.init(data);\n        meInstance.current.map.style.opacity = \"1\";\n    }, [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: mindmapEl,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\MindElixirReact.tsx\",\n        lineNumber: 91,\n        columnNumber: 12\n    }, undefined);\n}, \"tk/LufcUWzEIz5Ax0QD2xWd/TwI=\")), \"tk/LufcUWzEIz5Ax0QD2xWd/TwI=\");\n_c1 = MindElixirReact;\nMindElixirReact.displayName = \"MindElixirReact\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindElixirReact);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindElixirReact$forwardRef\");\n$RefreshReg$(_c1, \"MindElixirReact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindElixirReact.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/MindElixir.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/MindElixir.js ***!
  \***************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ A; }\n/* harmony export */ });\n(function(){\"use strict\";try{if(typeof document<\"u\"){var i=document.createElement(\"style\");i.appendChild(document.createTextNode(\".mind-elixir{--gap: 30px;--root-radius: 30px;--main-radius: 20px;--root-color: #ffffff;--root-bgcolor: #4c4f69;--main-color: #444446;--main-bgcolor: #ffffff;--topic-padding: 3px;--color: #777777;--bgcolor: #f6f6f6;--selected: #4dc4ff;--panel-color: #444446;--panel-bgcolor: #ffffff;--panel-border-color: #eaeaea;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0);font-family:-apple-system,BlinkMacSystemFont,Helvetica Neue,PingFang SC,Microsoft YaHei,Source Han Sans SC,Noto Sans CJK SC,WenQuanYi Micro Hei,sans-serif}.mind-elixir .hyper-link{text-decoration:none;margin-left:.3em}.map-container{-webkit-user-select:none;user-select:none;height:100%;width:100%;overflow:scroll;font-size:15px}.map-container *{box-sizing:border-box}.map-container::-webkit-scrollbar{width:0px;height:0px}.map-container .selected{box-shadow:0 0 0 2px var(--selected)}.map-container .lhs{direction:rtl}.map-container .lhs me-tpc{direction:ltr}.map-container .map-canvas{height:20000px;width:20000px;position:relative;-webkit-user-select:none;user-select:none;transition:transform .3s;transform:scale(1);background-color:var(--bgcolor)}.map-container .map-canvas me-nodes{position:absolute;display:flex;justify-content:center;align-items:center;height:fit-content;width:fit-content}.map-container .map-canvas me-root{position:relative}.map-container .map-canvas me-root me-tpc{display:block;font-size:25px;color:var(--root-color);padding:10px var(--gap);border-radius:var(--root-radius);white-space:pre-wrap;background-color:var(--root-bgcolor)}.map-container .map-canvas me-root me-tpc #input-box{padding:10px var(--gap)}.map-container me-main>me-wrapper{position:relative;margin:45px 65px}.map-container me-main>me-wrapper>me-parent{margin:10px;padding:0}.map-container me-main>me-wrapper>me-parent>me-tpc{border-radius:var(--main-radius);background-color:var(--main-bgcolor);border:2px solid var(--main-color);color:var(--main-color);padding:8px 25px}.map-container me-main>me-wrapper>me-parent>me-tpc #input-box{padding:8px 25px}.map-container me-wrapper{display:block;pointer-events:none;width:fit-content}.map-container me-children,.map-container me-parent{display:inline-block;vertical-align:middle}.map-container me-parent{position:relative;cursor:pointer;padding:6px var(--gap);margin-top:10px}.map-container me-parent me-tpc{position:relative;display:block;border-radius:3px;color:var(--color);pointer-events:all;max-width:35em;white-space:pre-wrap;padding:var(--topic-padding)}.map-container me-parent me-tpc .insert-preview{position:absolute;width:100%;left:0;z-index:9}.map-container me-parent me-tpc .show{background:#7ad5ff;pointer-events:none;opacity:.7}.map-container me-parent me-tpc .before{height:14px;top:-14px}.map-container me-parent me-tpc .in{height:100%;top:0}.map-container me-parent me-tpc .after{height:14px;bottom:-14px}.map-container me-parent me-epd{position:absolute;height:18px;width:18px;opacity:.8;background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgdD0iMTY1NjY1NDcxNzI0MiIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHZlcnNpb249IjEuMSIKICAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+CiAgICA8cGF0aCBkPSJNNTEyIDc0LjY2NjY2N0MyNzAuOTMzMzMzIDc0LjY2NjY2NyA3NC42NjY2NjcgMjcwLjkzMzMzMyA3NC42NjY2NjcgNTEyUzI3MC45MzMzMzMgOTQ5LjMzMzMzMyA1MTIgOTQ5LjMzMzMzMyA5NDkuMzMzMzMzIDc1My4wNjY2NjcgOTQ5LjMzMzMzMyA1MTIgNzUzLjA2NjY2NyA3NC42NjY2NjcgNTEyIDc0LjY2NjY2N3oiIHN0cm9rZS13aWR0aD0iNTQiIHN0cm9rZT0nYmxhY2snIGZpbGw9J3doaXRlJyA+PC9wYXRoPgogICAgPHBhdGggZD0iTTY4Mi42NjY2NjcgNDgwaC0xMzguNjY2NjY3VjM0MS4zMzMzMzNjMC0xNy4wNjY2NjctMTQuOTMzMzMzLTMyLTMyLTMycy0zMiAxNC45MzMzMzMtMzIgMzJ2MTM4LjY2NjY2N0gzNDEuMzMzMzMzYy0xNy4wNjY2NjcgMC0zMiAxNC45MzMzMzMtMzIgMzJzMTQuOTMzMzMzIDMyIDMyIDMyaDEzOC42NjY2NjdWNjgyLjY2NjY2N2MwIDE3LjA2NjY2NyAxNC45MzMzMzMgMzIgMzIgMzJzMzItMTQuOTMzMzMzIDMyLTMydi0xMzguNjY2NjY3SDY4Mi42NjY2NjdjMTcuMDY2NjY3IDAgMzItMTQuOTMzMzMzIDMyLTMycy0xNC45MzMzMzMtMzItMzItMzJ6Ij48L3BhdGg+Cjwvc3ZnPg==);background-repeat:no-repeat;background-size:contain;background-position:center;pointer-events:all;z-index:9}.map-container me-parent me-epd.minus{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgdD0iMTY1NjY1NTU2NDk4NSIgY2xhc3M9Imljb24iIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHZlcnNpb249IjEuMSIKICAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+CiAgICA8cGF0aCBkPSJNNTEyIDc0LjY2NjY2N0MyNzAuOTMzMzMzIDc0LjY2NjY2NyA3NC42NjY2NjcgMjcwLjkzMzMzMyA3NC42NjY2NjcgNTEyUzI3MC45MzMzMzMgOTQ5LjMzMzMzMyA1MTIgOTQ5LjMzMzMzMyA5NDkuMzMzMzMzIDc1My4wNjY2NjcgOTQ5LjMzMzMzMyA1MTIgNzUzLjA2NjY2NyA3NC42NjY2NjcgNTEyIDc0LjY2NjY2N3oiIHN0cm9rZS13aWR0aD0iNTQiIHN0cm9rZT0nYmxhY2snIGZpbGw9J3doaXRlJyA+PC9wYXRoPgogICAgPHBhdGggZD0iTTY4Mi42NjY2NjcgNTQ0SDM0MS4zMzMzMzNjLTE3LjA2NjY2NyAwLTMyLTE0LjkzMzMzMy0zMi0zMnMxNC45MzMzMzMtMzIgMzItMzJoMzQxLjMzMzMzNGMxNy4wNjY2NjcgMCAzMiAxNC45MzMzMzMgMzIgMzJzLTE0LjkzMzMzMyAzMi0zMiAzMnoiPjwvcGF0aD4KPC9zdmc+)!important;transition:opacity .3s;opacity:0}.map-container me-parent me-epd.minus:hover{opacity:.8}.map-container .icon{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.map-container .lines,.map-container .summary,.map-container .subLines,.map-container .topiclinks,.map-container .linkcontroller{position:absolute;height:102%;width:100%;top:0;left:0}.map-container .topiclinks,.map-container .linkcontroller,.map-container .summary{pointer-events:none}.map-container .topiclinks text,.map-container .linkcontroller text,.map-container .summary text{pointer-events:all}.map-container .topiclinks .selected,.map-container .linkcontroller .selected,.map-container .summary .selected{pointer-events:none}.map-container .lines,.map-container .subLines{pointer-events:none;z-index:-1}.map-container .topiclinks *,.map-container .linkcontroller *{z-index:100}.map-container .topiclinks g{cursor:pointer}.map-container #input-box{position:absolute;top:0;left:0;padding:var(--topic-padding);color:var(--color);background-color:var(--bgcolor);width:max-content;max-width:35em;z-index:11;direction:ltr;-webkit-user-select:auto;user-select:auto;pointer-events:all}.map-container me-tpc>*{pointer-events:none}.map-container me-tpc>a{pointer-events:auto}.map-container me-tpc>img{display:block;margin-bottom:8px;object-fit:cover}.map-container me-tpc>.text{display:inline-block}.map-container .circle{position:absolute;height:10px;width:10px;margin-top:-5px;margin-left:-5px;border-radius:100%;background:#757575;border:2px solid #ffffff;cursor:pointer}.map-container .tags{direction:ltr}.map-container .tags span{display:inline-block;border-radius:3px;padding:2px 4px;background:#d6f0f8;color:#276f86;margin:2px 3px 0 0;font-size:12px;line-height:1.3em}.map-container .icons{display:inline-block;direction:ltr;margin-right:10px}.map-container .icons span{display:inline-block;line-height:1.3em}.map-container .mind-elixir-ghost{position:fixed;top:-100%;left:-100%;box-sizing:content-box;opacity:.5;background-color:#f6f6f6;max-width:200px;width:fit-content;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;padding:8px 16px;border-radius:6px;border:#666666 2px solid}.map-container .selection-area{background:#4f90f22d;border:1px solid #4f90f2}.mind-elixir .context-menu{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99}.mind-elixir .context-menu .menu-list{position:fixed;list-style:none;margin:0;padding:0;font:300 15px Roboto,sans-serif;color:var(--panel-color);box-shadow:0 12px 15px #0003}.mind-elixir .context-menu .menu-list li{min-width:200px;overflow:hidden;white-space:nowrap;padding:10px 14px;background:var(--panel-bgcolor);border-bottom:1px solid var(--panel-border-color)}.mind-elixir .context-menu .menu-list li a{color:#333;text-decoration:none}.mind-elixir .context-menu .menu-list li.disabled{display:none;color:#5e5e5e;background-color:#f7f7f7}.mind-elixir .context-menu .menu-list li.disabled:hover{cursor:default;background-color:#f7f7f7}.mind-elixir .context-menu .menu-list li:hover{cursor:pointer;filter:brightness(.9)}.mind-elixir .context-menu .menu-list li:first-child{border-radius:5px 5px 0 0}.mind-elixir .context-menu .menu-list li:last-child{border-bottom:0;border-radius:0 0 5px 5px}.mind-elixir .context-menu .menu-list li span:last-child{float:right}.mind-elixir .tips{position:absolute;bottom:20px;left:50%;transform:translate(-50%);color:var(--panel-color);font-weight:bolder}.mind-elixir .mobile-menu{position:absolute;left:20px;bottom:70px;z-index:99;margin:0;padding:0;color:#333;border-radius:5px;box-shadow:0 12px 15px #0003;overflow:hidden}.mind-elixir .mobile-menu *{transition:color .4s,background-color .4s}.mind-elixir .mobile-menu div{float:left;text-align:center;width:30px;overflow:hidden;white-space:nowrap;padding:8px;background-color:#fff;border-bottom:1px solid #ecf0f1}.mind-elixir .mobile-menu div a{color:#333;text-decoration:none}.mind-elixir .mobile-menu div.disabled{color:#5e5e5e;background-color:#f7f7f7}.mind-elixir .mobile-menu div.disabled:hover{cursor:default;background-color:#f7f7f7}.mind-elixir .mobile-menu div:hover{cursor:pointer;background-color:#ecf0f1}.mind-elixir-toolbar{font-family:iconfont;position:absolute;color:var(--panel-color);background:var(--panel-bgcolor);padding:10px;border-radius:5px;box-shadow:0 1px 2px #0003}.mind-elixir-toolbar svg{display:inline-block}.mind-elixir-toolbar span:active{opacity:.5}.mind-elixir-toolbar.rb{right:20px;bottom:20px}.mind-elixir-toolbar.rb span+span{margin-left:10px}.mind-elixir-toolbar.lt{font-size:20px;left:20px;top:20px}.mind-elixir-toolbar.lt span{display:block}.mind-elixir-toolbar.lt span+span{margin-top:10px}\")),document.head.appendChild(i)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\n(function(e) {\n  var t, n, o, s, i, r, c = '<svg><symbol id=\"icon-edit\" viewBox=\"0 0 1024 1024\"><path d=\"M423.765333 128a42.666667 42.666667 0 0 1 3.2 85.205333L423.765333 213.333333H234.666667a64 64 0 0 0-63.872 60.245334L170.666667 277.333333v512a64 64 0 0 0 60.245333 63.872L234.666667 853.333333h512a64 64 0 0 0 63.872-60.245333L810.666667 789.333333v-189.098666a42.666667 42.666667 0 0 1 85.205333-3.2l0.128 3.2V789.333333a149.333333 149.333333 0 0 1-144.213333 149.248L746.666667 938.666667h-512a149.333333 149.333333 0 0 1-149.248-144.213334L85.333333 789.333333v-512a149.333333 149.333333 0 0 1 144.213334-149.248L234.666667 128h189.098666z m324.949334-53.248a42.666667 42.666667 0 0 1 60.330666 0l150.869334 150.869333a42.666667 42.666667 0 0 1 0 60.330667l-329.386667 329.386667a42.666667 42.666667 0 0 1-29.44 12.458666l-153.386667 2.517334a42.666667 42.666667 0 0 1-43.349333-43.349334l2.56-153.386666a42.666667 42.666667 0 0 1 12.458667-29.44z m30.165333 90.496L491.946667 452.266667l-1.493334 91.989333 92.032-1.493333 286.976-286.976-90.538666-90.538667z\"  ></path></symbol><symbol id=\"icon-rising\" viewBox=\"0 0 1024 1024\"><path d=\"M553.173333 803.84h-64l0.021334-474.581333-224.021334 224-45.269333-45.226667L521.6 206.293333l301.717333 301.696-45.269333 45.269334-224.853333-224.896v475.477333z\"  ></path></symbol><symbol id=\"icon-falling\" viewBox=\"0 0 1024 1024\"><path d=\"M553.173333 238.314667h-64l0.021334 474.602666-224.021334-224-45.269333 45.226667L521.6 835.861333l301.717333-301.717333-45.269333-45.226667-224.853333 224.853334V238.336z\"  ></path></symbol><symbol id=\"icon-shanchu2\" viewBox=\"0 0 1024 1024\"><path d=\"M516.60601807 107.93026734c-82.64382935 0-149.71865844 65.51751709-152.5729065 147.77160644H171.37136841c-21.40603638 0-38.92044068 17.38504028-38.92044068 38.92126465 0 21.40686036 17.38504028 38.92208862 38.92126466 38.92208862h42.94308471v435.40136719c0 81.73498536 55.39828492 148.55026245 123.90106201 148.55026245h348.99444581c68.37341309 0 123.90106201-66.42553711 123.901062-148.55026245V333.80477906h38.92126465c21.40686036 0 38.92126464-17.38586426 38.92126465-38.92208863 0-21.40686036-17.38504028-38.92126464-38.92126465-38.92126465H668.91854859C666.45321656 173.44860839 599.24902344 107.93109131 516.60601807 107.93109131z m-79.65939331 147.77160644c2.85424805-42.16442872 37.2354126-74.85809326 79.78875732-74.85809326s76.93450927 32.82302857 79.39984131 74.85809326H436.94662476z m-98.86047364 589.01165771c-24.2611084 0-50.98754883-31.13717651-50.98754883-75.76693725V333.80477906h450.97036744V769.33551026c0 44.50039673-26.72644043 75.76776123-50.98754884 75.76776122H338.08615112v-0.38973999z m0 0\"  ></path><path d=\"M390.37063599 751.17263794c17.77313232 0 32.43411255-17.7739563 32.43411255-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43411255-40.08966065-17.77478027 0-32.43493653 17.77478027-32.43493653 40.08966065v228.72875976c0 22.18469239 14.27124023 40.08883667 32.43493653 40.08883667z m117.41308594 0c17.7739563 0 32.43411255-17.7739563 32.43411255-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43411255-40.08966065-17.7739563 0-32.43493653 17.77478027-32.43493653 40.08966065v228.72875976c0 22.18469239 14.66098023 40.08883667 32.43493653 40.08883667z m123.51049804 0c17.7739563 0 32.43493653-17.7739563 32.43493652-40.08883667V482.35504151c0-22.31488037-14.53079224-40.08966065-32.43493652-40.08966065-17.7739563 0-32.43411255 17.77478027-32.43411255 40.08966065v228.72875976c0 22.18469239 14.14105224 40.08883667 32.43411255 40.08883667z m0 0\"  ></path></symbol><symbol id=\"icon-zijiedian\" viewBox=\"0 0 1024 1024\"><path d=\"M312.208 472c19.568-157.856 153.432-280 315.656-280 175.68 0 318.112 143.272 318.112 320S803.552 832 627.864 832c-162.224 0-296.08-122.144-315.656-280H120a40 40 0 0 1 0-80h192.208zM632 752c132.552 0 240-107.448 240-240 0-132.552-107.448-240-240-240-132.552 0-240 107.448-240 240 0 132.552 107.448 240 240 240z m-40-280v-80a40 40 0 0 1 80 0v80h80a40 40 0 0 1 0 80h-80v80a40 40 0 0 1-80 0v-80h-80a40 40 0 0 1 0-80h80z\"  ></path></symbol><symbol id=\"icon-tongjijiedian-\" viewBox=\"0 0 1024 1024\"><path d=\"M803.84 131.626667H410.24A59.733333 59.733333 0 0 0 350.506667 192v45.226667H199.68a51.626667 51.626667 0 0 0-51.626667 51.626666v465.92a51.626667 51.626667 0 0 0 51.626667 51.626667h187.52v-55.466667h-162.133333a21.333333 21.333333 0 0 1-21.333334-21.333333V313.386667a21.333333 21.333333 0 0 1 21.333334-21.333334h125.653333v64a59.733333 59.733333 0 0 0 59.733333 59.733334h393.386667a59.733333 59.733333 0 0 0 59.733333-59.733334V192a59.733333 59.733333 0 0 0-59.733333-60.373333z m4.266667 224.64a4.266667 4.266667 0 0 1-4.266667 4.266666H410.24a4.266667 4.266667 0 0 1-4.266667-4.266666V192a4.266667 4.266667 0 0 1 4.266667-4.266667h393.6a4.266667 4.266667 0 0 1 4.266667 4.266667zM716.16 749.44h-81.28v-81.493333a27.733333 27.733333 0 0 0-55.466667 0v81.28h-81.493333a27.733333 27.733333 0 1 0 0 55.466666h81.28v81.28a27.733333 27.733333 0 1 0 55.466667 0v-81.066666h81.28a27.733333 27.733333 0 0 0 0-55.466667z\"  ></path></symbol><symbol id=\"icon-close\" viewBox=\"0 0 1024 1024\"><path d=\"M557.312 513.248l265.28-263.904c12.544-12.48 12.608-32.704 0.128-45.248-12.512-12.576-32.704-12.608-45.248-0.128L512.128 467.904l-263.04-263.84c-12.448-12.48-32.704-12.544-45.248-0.064-12.512 12.48-12.544 32.736-0.064 45.28l262.976 263.776L201.6 776.8c-12.544 12.48-12.608 32.704-0.128 45.248a31.937 31.937 0 0 0 22.688 9.44c8.16 0 16.32-3.104 22.56-9.312l265.216-263.808 265.44 266.24c6.24 6.272 14.432 9.408 22.656 9.408a31.94 31.94 0 0 0 22.592-9.344c12.512-12.48 12.544-32.704 0.064-45.248L557.312 513.248z\" fill=\"\" ></path></symbol><symbol id=\"icon-menu\" viewBox=\"0 0 1024 1024\"><path d=\"M109.714 292.571h804.572c21.943 0 36.571-21.942 36.571-43.885 0-14.629-14.628-29.257-36.571-29.257H109.714c-21.943 0-36.571 14.628-36.571 36.571 0 14.629 14.628 36.571 36.571 36.571zM914.286 512H109.714c-21.943 0-36.571 14.629-36.571 36.571 0 14.629 14.628 36.572 36.571 36.572h804.572c21.943 0 36.571-21.943 36.571-43.886 0-14.628-14.628-29.257-36.571-29.257z m0 292.571H109.714c-21.943 0-36.571 14.629-36.571 36.572s14.628 36.571 36.571 36.571h804.572c21.943 0 36.571-21.943 36.571-36.571 0-21.943-14.628-36.572-36.571-36.572z\"  ></path></symbol><symbol id=\"icon-right\" viewBox=\"0 0 1024 1024\"><path d=\"M385 560.69999999L385 738.9c0 36.90000001 26.4 68.5 61.3 68.5l150.2 0c1.5 0 3-0.1 4.5-0.3 10.2 38.7 45.5 67.3 87.5 67.3 50 0 90.5-40.5 90.5-90.5s-40.5-90.5-90.5-90.5c-42 0-77.3 28.6-87.5 67.39999999-1.4-0.3-2.9-0.4-4.5-0.39999999L446.3 760.4c-6.8 0-14.3-8.9-14.3-21.49999999l0-427.00000001c0-12.7 7.40000001-21.5 14.30000001-21.5l150.19999999 0c1.5 0 3-0.2 4.5-0.4 10.2 38.8 45.5 67.3 87.5 67.3 50 0 90.5-40.5 90.5-90.4 0-49.9-40.5-90.6-90.5-90.59999999-42 0-77.3 28.6-87.5 67.39999999-1.4-0.2-2.9-0.4-4.49999999-0.4L446.3 243.3c-34.80000001 0-61.3 31.6-61.3 68.50000001L385 513.7l-79.1 0c-10.4-38.5-45.49999999-67-87.4-67-50 0-90.5 40.5-90.5 90.5s40.5 90.5 90.5 90.5c41.79999999 0 77.00000001-28.4 87.4-67L385 560.69999999z\" fill=\"\" ></path></symbol><symbol id=\"icon-left\" viewBox=\"0 0 1024 1024\"><path d=\"M639 463.30000001L639 285.1c0-36.90000001-26.4-68.5-61.3-68.5l-150.2 0c-1.5 0-3 0.1-4.5 0.3-10.2-38.7-45.5-67.3-87.5-67.3-50 0-90.5 40.5-90.5 90.5s40.5 90.5 90.5 90.5c42 0 77.3-28.6 87.5-67.39999999 1.4 0.3 2.9 0.4 4.5 0.39999999L577.7 263.6c6.8 0 14.3 8.9 14.3 21.49999999l0 427.00000001c0 12.7-7.40000001 21.5-14.30000001 21.5l-150.19999999 0c-1.5 0-3 0.2-4.5 0.4-10.2-38.8-45.5-67.3-87.5-67.3-50 0-90.5 40.5-90.5 90.4 0 49.9 40.5 90.6 90.5 90.59999999 42 0 77.3-28.6 87.5-67.39999999 1.4 0.2 2.9 0.4 4.49999999 0.4L577.7 780.7c34.80000001 0 61.3-31.6 61.3-68.50000001L639 510.3l79.1 0c10.4 38.5 45.49999999 67 87.4 67 50 0 90.5-40.5 90.5-90.5s-40.5-90.5-90.5-90.5c-41.79999999 0-77.00000001 28.4-87.4 67L639 463.30000001z\" fill=\"\" ></path></symbol><symbol id=\"icon-side\" viewBox=\"0 0 1024 1024\"><path d=\"M851.91168 328.45312c-59.97056 0-108.6208 48.47104-108.91264 108.36992l-137.92768 38.4a109.14304 109.14304 0 0 0-63.46752-46.58688l1.39264-137.11872c47.29344-11.86816 82.31936-54.66624 82.31936-105.64096 0-60.15488-48.76288-108.91776-108.91776-108.91776s-108.91776 48.76288-108.91776 108.91776c0 49.18784 32.60928 90.75712 77.38368 104.27392l-1.41312 138.87488a109.19936 109.19936 0 0 0-63.50336 48.55808l-138.93632-39.48544 0.01024-0.72704c0-60.15488-48.76288-108.91776-108.91776-108.91776s-108.91776 48.75776-108.91776 108.91776c0 60.15488 48.76288 108.91264 108.91776 108.91264 39.3984 0 73.91232-20.92032 93.03552-52.2496l139.19232 39.552-0.00512 0.2304c0 25.8304 9.00096 49.5616 24.02816 68.23424l-90.14272 132.63872a108.7488 108.7488 0 0 0-34.2528-5.504c-60.15488 0-108.91776 48.768-108.91776 108.91776 0 60.16 48.76288 108.91776 108.91776 108.91776 60.16 0 108.92288-48.75776 108.92288-108.91776 0-27.14624-9.9328-51.968-26.36288-71.04l89.04704-131.03104a108.544 108.544 0 0 0 37.6832 6.70208 108.672 108.672 0 0 0 36.48512-6.272l93.13792 132.57216a108.48256 108.48256 0 0 0-24.69888 69.0688c0 60.16 48.768 108.92288 108.91776 108.92288 60.16 0 108.91776-48.76288 108.91776-108.92288 0-60.14976-48.75776-108.91776-108.91776-108.91776a108.80512 108.80512 0 0 0-36.69504 6.3488l-93.07136-132.48a108.48768 108.48768 0 0 0 24.79616-72.22784l136.09984-37.888c18.99008 31.93856 53.84192 53.3504 93.69088 53.3504 60.16 0 108.92288-48.75776 108.92288-108.91264-0.00512-60.15488-48.77312-108.92288-108.92288-108.92288z\"  ></path></symbol><symbol id=\"icon-B\" viewBox=\"0 0 1024 1024\"><path d=\"M98.067692 65.457231H481.28c75.854769 0 132.411077 3.150769 169.668923 9.452307 37.336615 6.301538 70.656 19.534769 100.036923 39.620924 29.459692 20.007385 53.956923 46.710154 73.570462 80.029538 19.692308 33.398154 29.459692 70.734769 29.459692 112.167385 0 44.898462-12.130462 86.094769-36.233846 123.588923a224.886154 224.886154 0 0 1-98.461539 84.283077c58.368 17.092923 103.266462 46.08 134.695385 87.04 31.350154 40.96 47.025231 89.088 47.025231 144.462769 0 43.638154-10.082462 86.016-30.404923 127.212308-20.243692 41.196308-47.891692 74.043077-83.02277 98.697846-35.052308 24.654769-78.296615 39.778462-129.732923 45.449846-32.295385 3.465846-110.119385 5.671385-233.472 6.537846H98.067692V65.457231z m193.536 159.507692V446.621538h126.818462c75.460923 0 122.328615-1.024 140.603077-3.229538 33.083077-3.938462 59.155692-15.36 78.139077-34.343385 18.904615-18.904615 28.435692-43.874462 28.435692-74.830769 0-29.696-8.192-53.720615-24.497231-72.310154-16.384-18.510769-40.644923-29.696-72.940307-33.634461-19.140923-2.205538-74.279385-3.308308-165.415385-3.308308h-111.064615z m0 381.243077v256.315077h179.2c69.710769 0 113.979077-1.969231 132.726154-5.907692 28.750769-5.198769 52.145231-17.959385 70.262154-38.281847 18.116923-20.243692 27.096615-47.340308 27.096615-81.368615 0-28.750769-6.931692-53.169231-20.873846-73.255385a118.232615 118.232615 0 0 0-60.494769-43.795692c-26.387692-9.137231-83.574154-13.705846-171.638154-13.705846H291.603692z\"  ></path></symbol><symbol id=\"icon-a\" viewBox=\"0 0 1024 1024\"><path d=\"M757.76 665.6q0 20.48 1.536 34.304t7.68 22.016 18.944 12.288 34.304 4.096q-3.072 25.6-15.36 44.032-11.264 16.384-33.28 29.696t-62.976 13.312q-11.264 0-20.48-0.512t-17.408-2.56l-6.144-2.048-1.024 0q-4.096-1.024-10.24-4.096-2.048-2.048-4.096-2.048-1.024-1.024-2.048-1.024-14.336-8.192-23.552-17.408t-14.336-17.408q-6.144-10.24-9.216-20.48-63.488 75.776-178.176 75.776-48.128 0-88.064-15.36t-69.12-44.032-45.056-68.096-15.872-88.576 16.896-89.088 47.616-67.584 74.24-42.496 96.768-14.848q48.128 0 88.576 17.408t66.048 49.152q0-8.192 0.512-16.384t0.512-15.36q0-71.68-39.936-104.448t-128-32.768q-43.008 0-84.992 6.656t-84.992 17.92q14.336-28.672 25.088-47.616t24.064-29.184q30.72-24.576 158.72-24.576 79.872 0 135.168 13.824t90.624 43.52 51.2 75.264 15.872 108.032l0 200.704zM487.424 743.424q50.176 0 79.872-33.28t29.696-95.744q0-61.44-28.672-93.696t-76.8-32.256q-52.224 0-82.944 33.28t-30.72 94.72q0 58.368 31.744 92.672t77.824 34.304z\"  ></path></symbol><symbol id=\"icon-full\" viewBox=\"0 0 1024 1024\"><path d=\"M639.328 416c8.032 0 16.096-3.008 22.304-9.056l202.624-197.184-0.8 143.808c-0.096 17.696 14.144 32.096 31.808 32.192 0.064 0 0.128 0 0.192 0 17.6 0 31.904-14.208 32-31.808l1.248-222.208c0-0.672-0.352-1.248-0.384-1.92 0.032-0.512 0.288-0.896 0.288-1.408 0.032-17.664-14.272-32-31.968-32.032L671.552 96l-0.032 0c-17.664 0-31.968 14.304-32 31.968C639.488 145.632 653.824 160 671.488 160l151.872 0.224-206.368 200.8c-12.672 12.32-12.928 32.608-0.64 45.248C622.656 412.736 630.976 416 639.328 416z\"  ></path><path d=\"M896.032 639.552 896.032 639.552c-17.696 0-32 14.304-32.032 31.968l-0.224 151.872-200.832-206.4c-12.32-12.64-32.576-12.96-45.248-0.64-12.672 12.352-12.928 32.608-0.64 45.248l197.184 202.624-143.808-0.8c-0.064 0-0.128 0-0.192 0-17.6 0-31.904 14.208-32 31.808-0.096 17.696 14.144 32.096 31.808 32.192l222.24 1.248c0.064 0 0.128 0 0.192 0 0.64 0 1.12-0.32 1.76-0.352 0.512 0.032 0.896 0.288 1.408 0.288l0.032 0c17.664 0 31.968-14.304 32-31.968L928 671.584C928.032 653.952 913.728 639.584 896.032 639.552z\"  ></path><path d=\"M209.76 159.744l143.808 0.8c0.064 0 0.128 0 0.192 0 17.6 0 31.904-14.208 32-31.808 0.096-17.696-14.144-32.096-31.808-32.192L131.68 95.328c-0.064 0-0.128 0-0.192 0-0.672 0-1.248 0.352-1.888 0.384-0.448 0-0.8-0.256-1.248-0.256 0 0-0.032 0-0.032 0-17.664 0-31.968 14.304-32 31.968L96 352.448c-0.032 17.664 14.272 32 31.968 32.032 0 0 0.032 0 0.032 0 17.664 0 31.968-14.304 32-31.968l0.224-151.936 200.832 206.4c6.272 6.464 14.624 9.696 22.944 9.696 8.032 0 16.096-3.008 22.304-9.056 12.672-12.32 12.96-32.608 0.64-45.248L209.76 159.744z\"  ></path><path d=\"M362.368 617.056l-202.624 197.184 0.8-143.808c0.096-17.696-14.144-32.096-31.808-32.192-0.064 0-0.128 0-0.192 0-17.6 0-31.904 14.208-32 31.808l-1.248 222.24c0 0.704 0.352 1.312 0.384 2.016 0 0.448-0.256 0.832-0.256 1.312-0.032 17.664 14.272 32 31.968 32.032L352.448 928c0 0 0.032 0 0.032 0 17.664 0 31.968-14.304 32-31.968s-14.272-32-31.968-32.032l-151.936-0.224 206.4-200.832c12.672-12.352 12.96-32.608 0.64-45.248S375.008 604.704 362.368 617.056z\"  ></path></symbol><symbol id=\"icon-add\" viewBox=\"0 0 1024 1024\"><path d=\"M863.328 482.56l-317.344-1.12L545.984 162.816c0-17.664-14.336-32-32-32s-32 14.336-32 32l0 318.4L159.616 480.064c-0.032 0-0.064 0-0.096 0-17.632 0-31.936 14.24-32 31.904C127.424 529.632 141.728 544 159.392 544.064l322.592 1.152 0 319.168c0 17.696 14.336 32 32 32s32-14.304 32-32l0-318.944 317.088 1.12c0.064 0 0.096 0 0.128 0 17.632 0 31.936-14.24 32-31.904C895.264 496.992 880.96 482.624 863.328 482.56z\"  ></path></symbol><symbol id=\"icon-move\" viewBox=\"0 0 1024 1024\"><path d=\"M863.744 544 163.424 544c-17.664 0-32-14.336-32-32s14.336-32 32-32l700.32 0c17.696 0 32 14.336 32 32S881.44 544 863.744 544z\"  ></path></symbol><symbol id=\"icon-living\" viewBox=\"0 0 1024 1024\"><path d=\"M514.133333 488.533333m-106.666666 0a106.666667 106.666667 0 1 0 213.333333 0 106.666667 106.666667 0 1 0-213.333333 0Z\" fill=\"\" ></path><path d=\"M512 64C264.533333 64 64 264.533333 64 512c0 236.8 183.466667 428.8 416 445.866667v-134.4c-53.333333-59.733333-200.533333-230.4-200.533333-334.933334 0-130.133333 104.533333-234.666667 234.666666-234.666666s234.666667 104.533333 234.666667 234.666666c0 61.866667-49.066667 153.6-145.066667 270.933334l-59.733333 68.266666V960C776.533333 942.933333 960 748.8 960 512c0-247.466667-200.533333-448-448-448z\" fill=\"\" ></path></symbol></svg>', l = (l = document.getElementsByTagName(\"script\"))[l.length - 1].getAttribute(\"data-injectcss\");\n  if (l && !e.__iconfont__svg__cssinject__) {\n    e.__iconfont__svg__cssinject__ = !0;\n    try {\n      document.write(\n        \"<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>\"\n      );\n    } catch {\n    }\n  }\n  function h() {\n    i || (i = !0, o());\n  }\n  t = function() {\n    var a, d, u, g;\n    (g = document.createElement(\"div\")).innerHTML = c, c = null, (u = g.getElementsByTagName(\"svg\")[0]) && (u.setAttribute(\"aria-hidden\", \"true\"), u.style.position = \"absolute\", u.style.width = 0, u.style.height = 0, u.style.overflow = \"hidden\", a = u, (d = document.body).firstChild ? (g = a, (u = d.firstChild).parentNode.insertBefore(g, u)) : d.appendChild(a));\n  }, document.addEventListener ? ~[\"complete\", \"loaded\", \"interactive\"].indexOf(document.readyState) ? setTimeout(t, 0) : (n = function() {\n    document.removeEventListener(\"DOMContentLoaded\", n, !1), t();\n  }, document.addEventListener(\"DOMContentLoaded\", n, !1)) : document.attachEvent && (o = t, s = e.document, i = !1, (r = function() {\n    try {\n      s.documentElement.doScroll(\"left\");\n    } catch {\n      return void setTimeout(r, 50);\n    }\n    h();\n  })(), s.onreadystatechange = function() {\n    s.readyState == \"complete\" && (s.onreadystatechange = null, h());\n  });\n})(window);\nconst L = 0, R = 1, ce = 2, D = 30, $e = {\n  name: \"Latte\",\n  palette: [\"#dd7878\", \"#ea76cb\", \"#8839ef\", \"#e64553\", \"#fe640b\", \"#df8e1d\", \"#40a02b\", \"#209fb5\", \"#1e66f5\", \"#7287fd\"],\n  cssVar: {\n    \"--main-color\": \"#444446\",\n    \"--main-bgcolor\": \"#ffffff\",\n    \"--color\": \"#777777\",\n    \"--bgcolor\": \"#f6f6f6\",\n    \"--panel-color\": \"#444446\",\n    \"--panel-bgcolor\": \"#ffffff\",\n    \"--panel-border-color\": \"#eaeaea\"\n  }\n}, Pe = {\n  name: \"Dark\",\n  palette: [\"#848FA0\", \"#748BE9\", \"#D2F9FE\", \"#4145A5\", \"#789AFA\", \"#706CF4\", \"#EF987F\", \"#775DD5\", \"#FCEECF\", \"#DA7FBC\"],\n  cssVar: {\n    \"--main-color\": \"#ffffff\",\n    \"--main-bgcolor\": \"#4c4f69\",\n    \"--color\": \"#cccccc\",\n    \"--bgcolor\": \"#252526\",\n    \"--panel-color\": \"#ffffff\",\n    \"--panel-bgcolor\": \"#2d3748\",\n    \"--panel-border-color\": \"#696969\"\n  }\n};\nfunction oe(e) {\n  return e.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\");\n}\nconst nt = () => /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent), se = function(e, t) {\n  if (t.id === e)\n    return t;\n  if (t.children && t.children.length) {\n    for (let n = 0; n < t.children.length; n++) {\n      const o = se(e, t.children[n]);\n      if (o)\n        return o;\n    }\n    return null;\n  } else\n    return null;\n}, O = (e, t) => {\n  if (e.parent = t, e.children)\n    for (let n = 0; n < e.children.length; n++)\n      O(e.children[n], e);\n};\nfunction ue(e) {\n  if (e.id = G(), e.children)\n    for (let t = 0; t < e.children.length; t++)\n      ue(e.children[t]);\n}\nconst ot = (e, t) => {\n  let n = Date.now();\n  return function(...o) {\n    Date.now() - n < t || (e(...o), n = Date.now());\n  };\n};\nfunction He(e, t, n, o) {\n  const s = o - t, i = e - n;\n  let r = Math.atan(Math.abs(s) / Math.abs(i)) / 3.14 * 180;\n  i < 0 && s > 0 && (r = 180 - r), i < 0 && s < 0 && (r = 180 + r), i > 0 && s < 0 && (r = 360 - r);\n  const c = 15, l = 30, h = r + l, a = r - l;\n  return {\n    x1: n + Math.cos(Math.PI * h / 180) * c,\n    y1: o - Math.sin(Math.PI * h / 180) * c,\n    x2: n + Math.cos(Math.PI * a / 180) * c,\n    y2: o - Math.sin(Math.PI * a / 180) * c\n  };\n}\nfunction G() {\n  return ((/* @__PURE__ */ new Date()).getTime().toString(16) + Math.random().toString(16).substr(2)).substr(2, 16);\n}\nconst st = function() {\n  const e = G();\n  return {\n    topic: this.newTopicName,\n    id: e\n  };\n};\nfunction fe(e) {\n  return JSON.parse(\n    JSON.stringify(e, (n, o) => {\n      if (n !== \"parent\")\n        return o;\n    })\n  );\n}\nconst $ = (e, t) => {\n  let n = 0, o = 0;\n  for (; t && t !== e; )\n    n += t.offsetLeft, o += t.offsetTop, t = t.offsetParent;\n  return { offsetLeft: n, offsetTop: o };\n}, N = (e, t) => {\n  for (const n in t)\n    e.setAttribute(n, t[n]);\n}, ae = (e) => e ? e.tagName === \"ME-TPC\" : !1, le = (e) => e.filter((t) => {\n  for (let n = 0; n < e.length; n++) {\n    if (t === e[n])\n      continue;\n    if (e[n].parentElement.parentElement.contains(t))\n      return !1;\n  }\n  return !0;\n}), k = {\n  moved: !1,\n  // diffrentiate click and move\n  mousedown: !1,\n  onMove(e, t) {\n    if (this.mousedown) {\n      this.moved = !0;\n      const n = e.movementX, o = e.movementY;\n      t.scrollTo(t.scrollLeft - n, t.scrollTop - o);\n    }\n  },\n  clear() {\n    this.moved = !1, this.mousedown = !1;\n  }\n};\nfunction it(e) {\n  e.map.addEventListener(\"click\", (t) => {\n    var o, s;\n    if (t.button !== 0)\n      return;\n    if ((o = e.helper1) != null && o.moved) {\n      e.helper1.clear();\n      return;\n    }\n    if ((s = e.helper2) != null && s.moved) {\n      e.helper2.clear();\n      return;\n    }\n    if (k.moved) {\n      k.clear();\n      return;\n    }\n    e.clearSelection();\n    const n = t.target;\n    if (n.tagName === \"ME-EPD\")\n      e.expandNode(n.previousSibling);\n    else if (ae(n))\n      e.selectNode(n, !1, t);\n    else if (e.editable)\n      n.tagName === \"text\" ? n.dataset.type === \"custom-link\" ? e.selectArrow(n.parentElement) : e.selectSummary(n.parentElement) : n.className;\n    else\n      return;\n  }), e.map.addEventListener(\"dblclick\", (t) => {\n    if (t.preventDefault(), !e.editable)\n      return;\n    const n = t.target;\n    ae(n) ? e.beginEdit(n) : n.tagName === \"text\" && (n.dataset.type === \"custom-link\" ? e.editArrowLabel(n.parentElement) : e.editSummary(n.parentElement));\n  }), e.map.addEventListener(\"mousemove\", (t) => {\n    t.target.contentEditable !== \"true\" && k.onMove(t, e.container);\n  }), e.map.addEventListener(\"mousedown\", (t) => {\n    const n = e.mouseSelectionButton === 0 ? 2 : 0;\n    t.button === n && t.target.contentEditable !== \"true\" && (k.moved = !1, k.mousedown = !0);\n  }), e.map.addEventListener(\"mouseleave\", (t) => {\n    const n = e.mouseSelectionButton === 0 ? 2 : 0;\n    t.button === n && k.clear();\n  }), e.map.addEventListener(\"mouseup\", (t) => {\n    const n = e.mouseSelectionButton === 0 ? 2 : 0;\n    t.button === n && k.clear();\n  }), e.map.addEventListener(\"contextmenu\", (t) => {\n    t.preventDefault();\n  });\n}\nconst rt = {\n  create() {\n    return {\n      handlers: {},\n      showHandler: function() {\n      },\n      addListener: function(e, t) {\n        this.handlers[e] === void 0 && (this.handlers[e] = []), this.handlers[e].push(t);\n      },\n      fire: function(e, ...t) {\n        if (this.handlers[e] instanceof Array) {\n          const n = this.handlers[e];\n          for (let o = 0; o < n.length; o++)\n            n[o](...t);\n        }\n      },\n      removeListener: function(e, t) {\n        if (!this.handlers[e])\n          return;\n        const n = this.handlers[e];\n        if (!t)\n          n.length = 0;\n        else if (n.length)\n          for (let o = 0; o < n.length; o++)\n            n[o] === t && this.handlers[e].splice(o, 1);\n      }\n    };\n  }\n}, ie = document, ct = function() {\n  this.nodes.innerHTML = \"\";\n  const e = this.createTopic(this.nodeData);\n  pe(e, this.nodeData), e.draggable = !1;\n  const t = ie.createElement(\"me-root\");\n  t.appendChild(e);\n  const n = this.nodeData.children || [];\n  if (this.direction === ce) {\n    let o = 0, s = 0;\n    n.map((i) => {\n      i.direction === L ? o += 1 : i.direction === R ? s += 1 : o <= s ? (i.direction = L, o += 1) : (i.direction = R, s += 1);\n    });\n  }\n  lt(this, n, t);\n}, lt = function(e, t, n) {\n  const o = ie.createElement(\"me-main\");\n  o.className = \"lhs\";\n  const s = ie.createElement(\"me-main\");\n  s.className = \"rhs\";\n  for (let i = 0; i < t.length; i++) {\n    const r = t[i], { grp: c } = e.createWrapper(r);\n    e.direction === ce ? r.direction === L ? o.appendChild(c) : s.appendChild(c) : e.direction === L ? o.appendChild(c) : s.appendChild(c);\n  }\n  e.nodes.appendChild(o), e.nodes.appendChild(n), e.nodes.appendChild(s), e.nodes.appendChild(e.lines);\n}, at = function(e, t) {\n  const n = ie.createElement(\"me-children\");\n  for (let o = 0; o < t.length; o++) {\n    const s = t[o], { grp: i } = e.createWrapper(s);\n    n.appendChild(i);\n  }\n  return n;\n}, S = document, C = (e, t) => {\n  const o = (t ? t.mindElixirBox : S).querySelector(`[data-nodeid=me${e}]`);\n  if (!o)\n    throw new Error(`FindEle: Node ${e} not found, maybe it's collapsed.`);\n  return o;\n}, pe = function(e, t) {\n  if (e.innerHTML = \"\", t.style && (e.style.color = t.style.color || \"\", e.style.background = t.style.background || \"\", e.style.fontSize = t.style.fontSize + \"px\", e.style.fontWeight = t.style.fontWeight || \"normal\"), t.dangerouslySetInnerHTML) {\n    e.innerHTML = t.dangerouslySetInnerHTML;\n    return;\n  }\n  if (t.image) {\n    const n = t.image;\n    if (n.url && n.width && n.height) {\n      const o = S.createElement(\"img\");\n      o.src = n.url, o.style.width = n.width + \"px\", o.style.height = n.height + \"px\", e.appendChild(o), e.image = o;\n    }\n  } else\n    e.image && (e.image = void 0);\n  {\n    const n = S.createElement(\"span\");\n    n.className = \"text\", n.textContent = t.topic, e.appendChild(n), e.text = n;\n  }\n  if (t.hyperLink) {\n    const n = S.createElement(\"a\");\n    n.className = \"hyper-link\", n.target = \"_blank\", n.innerText = \"🔗\", n.href = t.hyperLink, e.appendChild(n), e.linkContainer = n;\n  } else\n    e.linkContainer && (e.linkContainer = void 0);\n  if (t.icons && t.icons.length) {\n    const n = S.createElement(\"span\");\n    n.className = \"icons\", n.innerHTML = t.icons.map((o) => `<span>${oe(o)}</span>`).join(\"\"), e.appendChild(n), e.icons = n;\n  } else\n    e.icons && (e.icons = void 0);\n  if (t.tags && t.tags.length) {\n    const n = S.createElement(\"div\");\n    n.className = \"tags\", n.innerHTML = t.tags.map((o) => `<span>${oe(o)}</span>`).join(\"\"), e.appendChild(n), e.tags = n;\n  } else\n    e.tags && (e.tags = void 0);\n}, dt = function(e, t) {\n  const n = S.createElement(\"me-wrapper\"), { p: o, tpc: s } = this.createParent(e);\n  if (n.appendChild(o), !t && e.children && e.children.length > 0) {\n    const i = me(e.expanded);\n    if (o.appendChild(i), e.expanded !== !1) {\n      const r = at(this, e.children);\n      n.appendChild(r);\n    }\n  }\n  return { grp: n, top: o, tpc: s };\n}, ht = function(e) {\n  const t = S.createElement(\"me-parent\"), n = this.createTopic(e);\n  return pe(n, e), t.appendChild(n), { p: t, tpc: n };\n}, ut = function(e) {\n  const t = S.createElement(\"me-children\");\n  return t.append(...e), t;\n}, ft = function(e) {\n  const t = S.createElement(\"me-tpc\");\n  return t.nodeObj = e, t.dataset.nodeid = \"me\" + e.id, t.draggable = this.draggable, t;\n};\nfunction Be(e) {\n  const t = S.createRange();\n  t.selectNodeContents(e);\n  const n = window.getSelection();\n  n && (n.removeAllRanges(), n.addRange(t));\n}\nconst pt = function(e) {\n  if (!e)\n    return;\n  const t = S.createElement(\"div\"), n = e.text.textContent;\n  e.appendChild(t), t.id = \"input-box\", t.textContent = n, t.contentEditable = \"true\", t.spellcheck = !1, t.style.cssText = `min-width:${e.offsetWidth - 8}px;`, this.direction === L && (t.style.right = \"0\"), t.focus(), Be(t), this.bus.fire(\"operation\", {\n    name: \"beginEdit\",\n    obj: e.nodeObj\n  }), t.addEventListener(\"keydown\", (o) => {\n    o.stopPropagation();\n    const s = o.key;\n    if (s === \"Enter\" || s === \"Tab\") {\n      if (o.shiftKey)\n        return;\n      o.preventDefault(), t == null || t.blur(), this.map.focus();\n    }\n  }), t.addEventListener(\"blur\", () => {\n    var i;\n    if (!t)\n      return;\n    const o = e.nodeObj, s = ((i = t.textContent) == null ? void 0 : i.trim()) || \"\";\n    s === \"\" ? o.topic = n : o.topic = s, t.remove(), s !== n && (e.text.textContent = o.topic, this.linkDiv(), this.bus.fire(\"operation\", {\n      name: \"finishEdit\",\n      obj: o,\n      origin: n\n    }));\n  });\n}, me = function(e) {\n  const t = S.createElement(\"me-epd\");\n  return t.expanded = e !== !1, t.className = e !== !1 ? \"minus\" : \"\", t;\n}, K = document, V = \"http://www.w3.org/2000/svg\", ze = function(e, t, n) {\n  const o = K.createElementNS(V, \"path\");\n  return N(o, {\n    d: e,\n    stroke: t || \"#666\",\n    fill: \"none\",\n    \"stroke-width\": n\n  }), o;\n}, X = function(e) {\n  const t = K.createElementNS(V, \"svg\");\n  return t.setAttribute(\"class\", e), t.setAttribute(\"overflow\", \"visible\"), t;\n}, ye = function() {\n  const e = K.createElementNS(V, \"line\");\n  return e.setAttribute(\"stroke\", \"#bbb\"), e.setAttribute(\"fill\", \"none\"), e.setAttribute(\"stroke-width\", \"2\"), e;\n}, mt = function(e, t) {\n  const n = {\n    stroke: \"rgb(235, 95, 82)\",\n    fill: \"none\",\n    \"stroke-linecap\": \"cap\",\n    \"stroke-width\": \"2\"\n  }, o = K.createElementNS(V, \"g\"), s = K.createElementNS(V, \"path\"), i = K.createElementNS(V, \"path\");\n  return N(i, {\n    d: t,\n    ...n\n  }), N(s, {\n    d: e,\n    ...n,\n    \"stroke-dasharray\": \"8,2\"\n  }), o.appendChild(s), o.appendChild(i), o;\n}, Re = function(e, t, n) {\n  if (!t)\n    return;\n  const o = document.createElement(\"div\");\n  e.nodes.appendChild(o);\n  const s = t.innerHTML;\n  o.id = \"input-box\", o.textContent = s, o.contentEditable = \"true\", o.spellcheck = !1;\n  const i = t.getAttribute(\"x\"), r = t.getAttribute(\"y\");\n  o.style.cssText = `min-width:88px;position:absolute;left:${i}px;top:${r}px;`;\n  const c = t.getAttribute(\"text-anchor\");\n  c === \"end\" ? o.style.cssText += \"transform: translate(-100%, -100%);\" : c === \"middle\" ? o.style.cssText += \"transform: translate(-50%, -100%);\" : o.style.cssText += \"transform: translate(0, -100%);\", o.focus(), Be(o), o.addEventListener(\"keydown\", (l) => {\n    l.stopPropagation();\n    const h = l.key;\n    if (h === \"Enter\" || h === \"Tab\") {\n      if (l.shiftKey)\n        return;\n      l.preventDefault(), o.blur(), e.map.focus();\n    }\n  }), o.addEventListener(\"blur\", () => {\n    o && n(o);\n  });\n}, gt = function(e) {\n  const t = this.map.querySelector(\"me-root\"), n = t.offsetTop, o = t.offsetLeft, s = t.offsetWidth, i = t.offsetHeight;\n  this.nodes.style.top = `${1e4 - this.nodes.offsetHeight / 2}px`, this.nodes.style.left = `${1e4 - o - s / 2}px`;\n  const r = this.map.querySelectorAll(\"me-main > me-wrapper\");\n  this.lines.innerHTML = \"\";\n  for (let c = 0; c < r.length; c++) {\n    const l = r[c], h = l.querySelector(\"me-tpc\"), { offsetLeft: a, offsetTop: d } = $(this.nodes, h), u = h.offsetWidth, g = h.offsetHeight, p = l.parentNode.className, m = this.generateMainBranch({ pT: n, pL: o, pW: s, pH: i, cT: d, cL: a, cW: u, cH: g, direction: p, containerHeight: this.nodes.offsetHeight }), f = this.theme.palette, v = h.nodeObj.branchColor || f[c % f.length];\n    h.style.borderColor = v, this.lines.appendChild(ze(m, v, \"3\"));\n    const b = l.children[0].children[1];\n    if (b && (b.style.top = (b.parentNode.offsetHeight - b.offsetHeight) / 2 + \"px\", p === \"lhs\" ? b.style.left = \"-10px\" : b.style.right = \"-10px\"), e && e !== l)\n      continue;\n    const y = X(\"subLines\"), x = l.lastChild;\n    x.tagName === \"svg\" && x.remove(), l.appendChild(y), qe(this, y, v, l, p, !0);\n  }\n  this.renderArrow(), this.renderSummary();\n}, qe = function(e, t, n, o, s, i) {\n  const r = o.firstChild, c = o.children[1].children;\n  if (c.length === 0)\n    return;\n  const l = r.offsetTop, h = r.offsetLeft, a = r.offsetWidth, d = r.offsetHeight;\n  for (let u = 0; u < c.length; u++) {\n    const g = c[u], p = g.firstChild, m = p.offsetTop, f = p.offsetLeft, v = p.offsetWidth, b = p.offsetHeight, y = p.firstChild.nodeObj.branchColor || n, x = e.generateSubBranch({ pT: l, pL: h, pW: a, pH: d, cT: m, cL: f, cW: v, cH: b, direction: s, isFirst: i });\n    t.appendChild(ze(x, y, \"2\"));\n    const w = p.children[1];\n    if (w) {\n      if (w.style.bottom = -(w.offsetHeight / 2) + \"px\", s === \"lhs\" ? w.style.left = \"10px\" : s === \"rhs\" && (w.style.right = \"10px\"), !w.expanded)\n        continue;\n    } else\n      continue;\n    qe(e, t, y, g, s);\n  }\n}, xe = {\n  addChild: \"插入子节点\",\n  addParent: \"插入父节点\",\n  addSibling: \"插入同级节点\",\n  removeNode: \"删除节点\",\n  focus: \"专注\",\n  cancelFocus: \"取消专注\",\n  moveUp: \"上移\",\n  moveDown: \"下移\",\n  link: \"连接\",\n  clickTips: \"请点击目标节点\",\n  summary: \"摘要\"\n}, we = {\n  cn: xe,\n  zh_CN: xe,\n  zh_TW: {\n    addChild: \"插入子節點\",\n    addParent: \"插入父節點\",\n    addSibling: \"插入同級節點\",\n    removeNode: \"刪除節點\",\n    focus: \"專注\",\n    cancelFocus: \"取消專注\",\n    moveUp: \"上移\",\n    moveDown: \"下移\",\n    link: \"連接\",\n    clickTips: \"請點擊目標節點\",\n    summary: \"摘要\"\n  },\n  en: {\n    addChild: \"Add child\",\n    addParent: \"Add parent\",\n    addSibling: \"Add sibling\",\n    removeNode: \"Remove node\",\n    focus: \"Focus Mode\",\n    cancelFocus: \"Cancel Focus Mode\",\n    moveUp: \"Move up\",\n    moveDown: \"Move down\",\n    link: \"Link\",\n    clickTips: \"Please click the target node\",\n    summary: \"Summary\"\n  },\n  ru: {\n    addChild: \"Добавить дочерний элемент\",\n    addParent: \"Добавить родительский элемент\",\n    addSibling: \"Добавить на этом уровне\",\n    removeNode: \"Удалить узел\",\n    focus: \"Режим фокусировки\",\n    cancelFocus: \"Отменить режим фокусировки\",\n    moveUp: \"Поднять выше\",\n    moveDown: \"Опустить ниже\",\n    link: \"Ссылка\",\n    clickTips: \"Пожалуйста, нажмите на целевой узел\",\n    summary: \"Описание\"\n  },\n  ja: {\n    addChild: \"子ノードを追加する\",\n    addParent: \"親ノードを追加します\",\n    addSibling: \"兄弟ノードを追加する\",\n    removeNode: \"ノードを削除\",\n    focus: \"集中\",\n    cancelFocus: \"集中解除\",\n    moveUp: \"上へ移動\",\n    moveDown: \"下へ移動\",\n    link: \"コネクト\",\n    clickTips: \"ターゲットノードをクリックしてください\",\n    summary: \"概要\"\n  },\n  pt: {\n    addChild: \"Adicionar item filho\",\n    addParent: \"Adicionar item pai\",\n    addSibling: \"Adicionar item irmao\",\n    removeNode: \"Remover item\",\n    focus: \"Modo Foco\",\n    cancelFocus: \"Cancelar Modo Foco\",\n    moveUp: \"Mover para cima\",\n    moveDown: \"Mover para baixo\",\n    link: \"Link\",\n    clickTips: \"Favor clicar no item alvo\",\n    summary: \"Resumo\"\n  },\n  it: {\n    addChild: \"Aggiungi figlio\",\n    addParent: \"Aggiungi genitore\",\n    addSibling: \"Aggiungi fratello\",\n    removeNode: \"Rimuovi nodo\",\n    focus: \"Modalità Focus\",\n    cancelFocus: \"Annulla Modalità Focus\",\n    moveUp: \"Sposta su\",\n    moveDown: \"Sposta giù\",\n    link: \"Collega\",\n    clickTips: \"Si prega di fare clic sul nodo di destinazione\",\n    summary: \"Unisci nodi\"\n  },\n  es: {\n    addChild: \"Agregar hijo\",\n    addParent: \"Agregar padre\",\n    addSibling: \"Agregar hermano\",\n    removeNode: \"Eliminar nodo\",\n    focus: \"Modo Enfoque\",\n    cancelFocus: \"Cancelar Modo Enfoque\",\n    moveUp: \"Mover hacia arriba\",\n    moveDown: \"Mover hacia abajo\",\n    link: \"Enlace\",\n    clickTips: \"Por favor haga clic en el nodo de destino\",\n    summary: \"Resumen\"\n  }\n};\nfunction vt(e, t) {\n  const n = (y) => {\n    const x = document.createElement(\"div\");\n    return x.innerText = y, x.className = \"tips\", x;\n  }, o = (y, x, w) => {\n    const _ = document.createElement(\"li\");\n    return _.id = y, _.innerHTML = `<span>${oe(x)}</span><span>${oe(w)}</span>`, _;\n  }, s = we[e.locale] ? e.locale : \"en\", i = we[s], r = o(\"cm-add_child\", i.addChild, \"tab\"), c = o(\"cm-add_parent\", i.addParent, \"\"), l = o(\"cm-add_sibling\", i.addSibling, \"enter\"), h = o(\"cm-remove_child\", i.removeNode, \"delete\"), a = o(\"cm-fucus\", i.focus, \"\"), d = o(\"cm-unfucus\", i.cancelFocus, \"\"), u = o(\"cm-up\", i.moveUp, \"PgUp\"), g = o(\"cm-down\", i.moveDown, \"Pgdn\"), p = o(\"cm-link\", i.link, \"\"), m = o(\"cm-summary\", i.summary, \"\"), f = document.createElement(\"ul\");\n  if (f.className = \"menu-list\", f.appendChild(r), f.appendChild(c), f.appendChild(l), f.appendChild(h), (!t || t.focus) && (f.appendChild(a), f.appendChild(d)), f.appendChild(u), f.appendChild(g), f.appendChild(m), (!t || t.link) && f.appendChild(p), t && t.extend)\n    for (let y = 0; y < t.extend.length; y++) {\n      const x = t.extend[y], w = o(x.name, x.name, x.key || \"\");\n      f.appendChild(w), w.onclick = (_) => {\n        x.onclick(_);\n      };\n    }\n  const v = document.createElement(\"div\");\n  v.className = \"context-menu\", v.appendChild(f), v.hidden = !0, e.container.append(v);\n  let b = !0;\n  return e.container.oncontextmenu = function(y) {\n    if (y.preventDefault(), !e.editable)\n      return;\n    const x = y.target;\n    if (ae(x)) {\n      x.parentElement.tagName === \"ME-ROOT\" ? b = !0 : b = !1, b ? (a.className = \"disabled\", u.className = \"disabled\", g.className = \"disabled\", c.className = \"disabled\", l.className = \"disabled\", h.className = \"disabled\") : (a.className = \"\", u.className = \"\", g.className = \"\", c.className = \"\", l.className = \"\", h.className = \"\"), e.currentNodes || e.selectNode(x), v.hidden = !1, k.mousedown && (k.mousedown = !1), f.style.top = \"\", f.style.bottom = \"\", f.style.left = \"\", f.style.right = \"\";\n      const w = f.getBoundingClientRect(), _ = f.offsetHeight, P = f.offsetWidth, U = y.clientY - w.top, H = y.clientX - w.left;\n      _ + U > window.innerHeight ? (f.style.top = \"\", f.style.bottom = \"0px\") : (f.style.bottom = \"\", f.style.top = U + 15 + \"px\"), P + H > window.innerWidth ? (f.style.left = \"\", f.style.right = \"0px\") : (f.style.right = \"\", f.style.left = H + 10 + \"px\");\n    }\n  }, v.onclick = (y) => {\n    y.target === v && (v.hidden = !0);\n  }, r.onclick = () => {\n    e.addChild(), v.hidden = !0;\n  }, c.onclick = () => {\n    e.insertParent(), v.hidden = !0;\n  }, l.onclick = () => {\n    b || (e.insertSibling(\"after\"), v.hidden = !0);\n  }, h.onclick = () => {\n    b || (e.removeNode(), v.hidden = !0);\n  }, a.onclick = () => {\n    b || (e.focusNode(e.currentNode), v.hidden = !0);\n  }, d.onclick = () => {\n    e.cancelFocus(), v.hidden = !0;\n  }, u.onclick = () => {\n    b || (e.moveUpNode(), v.hidden = !0);\n  }, g.onclick = () => {\n    b || (e.moveDownNode(), v.hidden = !0);\n  }, p.onclick = () => {\n    v.hidden = !0;\n    const y = e.currentNode, x = n(i.clickTips);\n    e.container.appendChild(x), e.map.addEventListener(\n      \"click\",\n      (w) => {\n        w.preventDefault(), x.remove();\n        const _ = w.target;\n        (_.parentElement.tagName === \"ME-PARENT\" || _.parentElement.tagName === \"ME-ROOT\") && e.createArrow(y, _);\n      },\n      {\n        once: !0\n      }\n    );\n  }, m.onclick = () => {\n    v.hidden = !0, e.createSummary(), e.unselectNodes();\n  }, () => {\n    r.onclick = null, c.onclick = null, l.onclick = null, h.onclick = null, a.onclick = null, d.onclick = null, u.onclick = null, g.onclick = null, p.onclick = null, m.onclick = null, v.onclick = null, e.container.oncontextmenu = null;\n  };\n}\nconst bt = (e) => {\n  const t = e.map.querySelectorAll(\".lhs>me-wrapper>me-parent>me-tpc\");\n  e.selectNode(t[Math.ceil(t.length / 2) - 1]);\n}, yt = (e) => {\n  const t = e.map.querySelectorAll(\".rhs>me-wrapper>me-parent>me-tpc\");\n  e.selectNode(t[Math.ceil(t.length / 2) - 1]);\n}, xt = (e) => {\n  e.selectNode(e.map.querySelector(\"me-root>me-tpc\"));\n}, wt = function(e, t) {\n  const n = t.parentElement.parentElement.parentElement.previousSibling;\n  if (n) {\n    const o = n.firstChild;\n    e.selectNode(o);\n  }\n}, Et = function(e, t) {\n  const n = t.parentElement.nextSibling;\n  if (n && n.firstChild) {\n    const o = n.firstChild.firstChild.firstChild;\n    e.selectNode(o);\n  }\n}, Ee = function(e, t) {\n  var i, r;\n  const n = e.currentNode || ((i = e.currentNodes) == null ? void 0 : i[0]);\n  if (!n)\n    return;\n  const o = n.nodeObj, s = n.offsetParent.offsetParent.parentElement;\n  o.parent ? s.className === t ? Et(e, n) : (r = o.parent) != null && r.parent ? wt(e, n) : xt(e) : t === \"lhs\" ? bt(e) : yt(e);\n}, Ne = function(e, t) {\n  var r;\n  const n = e.currentNode || ((r = e.currentNodes) == null ? void 0 : r[0]);\n  if (!n || !n.nodeObj.parent)\n    return;\n  const s = t + \"Sibling\", i = n.parentElement.parentElement[s];\n  i && e.selectNode(i.firstChild.firstChild);\n}, Q = function(e, t, n = 1) {\n  switch (t) {\n    case \"in\":\n      if (e.scaleVal * n > 1.6)\n        return;\n      e.scale(e.scaleVal += 0.2);\n      break;\n    case \"out\":\n      if (e.scaleVal * n < 0.6)\n        return;\n      e.scale(e.scaleVal -= 0.2);\n  }\n};\nfunction Nt(e) {\n  const t = () => {\n    e.currentArrow ? e.removeArrow() : e.currentSummary ? e.removeSummary(e.currentSummary.summaryObj.id) : e.currentNode ? e.removeNode() : e.currentNodes && e.removeNodes(e.currentNodes);\n  }, n = {\n    Enter: (o) => {\n      o.shiftKey ? e.insertSibling(\"before\") : o.ctrlKey ? e.insertParent() : e.insertSibling(\"after\");\n    },\n    Tab: () => {\n      e.addChild();\n    },\n    F1: () => {\n      e.toCenter();\n    },\n    F2: () => {\n      e.beginEdit();\n    },\n    ArrowUp: (o) => {\n      if (o.altKey)\n        e.moveUpNode();\n      else {\n        if (o.metaKey || o.ctrlKey)\n          return e.initSide();\n        Ne(e, \"previous\");\n      }\n    },\n    ArrowDown: (o) => {\n      o.altKey ? e.moveDownNode() : Ne(e, \"next\");\n    },\n    ArrowLeft: (o) => {\n      if (o.metaKey || o.ctrlKey)\n        return e.initLeft();\n      Ee(e, \"lhs\");\n    },\n    ArrowRight: (o) => {\n      if (o.metaKey || o.ctrlKey)\n        return e.initRight();\n      Ee(e, \"rhs\");\n    },\n    PageUp: () => e.moveUpNode(),\n    PageDown: () => {\n      e.moveDownNode();\n    },\n    c: (o) => {\n      (o.metaKey || o.ctrlKey) && (e.currentNode ? e.waitCopy = [e.currentNode] : e.currentNodes && (e.waitCopy = e.currentNodes));\n    },\n    x: (o) => {\n      (o.metaKey || o.ctrlKey) && (e.currentNode ? e.waitCopy = [e.currentNode] : e.currentNodes && (e.waitCopy = e.currentNodes), t());\n    },\n    v: (o) => {\n      !e.waitCopy || !e.currentNode || (o.metaKey || o.ctrlKey) && (e.waitCopy.length === 1 ? e.copyNode(e.waitCopy[0], e.currentNode) : e.copyNodes(e.waitCopy, e.currentNode));\n    },\n    \"+\": (o) => {\n      (o.metaKey || o.ctrlKey) && Q(e, \"in\");\n    },\n    \"-\": (o) => {\n      (o.metaKey || o.ctrlKey) && Q(e, \"out\");\n    },\n    0: (o) => {\n      (o.metaKey || o.ctrlKey) && e.scale(1);\n    },\n    Delete: t,\n    Backspace: t\n  };\n  e.map.onkeydown = (o) => {\n    if (o.preventDefault(), !e.editable || o.target !== o.currentTarget)\n      return;\n    const s = n[o.key];\n    s && s(o);\n  }, e.map.onwheel = (o) => {\n    if (o.ctrlKey || o.metaKey) {\n      o.preventDefault();\n      const s = Math.abs(o.deltaY / 100);\n      o.deltaY < 0 ? Q(e, \"in\", s) : e.scaleVal - 0.2 > 0 && Q(e, \"out\", s), o.stopPropagation();\n    }\n  };\n}\nfunction Ct(e, t) {\n  const n = (u, g) => {\n    const p = document.createElement(\"div\");\n    return p.id = u, p.innerHTML = `<svg class=\"icon\" aria-hidden=\"true\">\n    <use xlink:href=\"#icon-${g}\"></use>\n  </svg>`, p;\n  }, o = n(\"cm-add_child\", \"zijiedian\"), s = n(\"cm-add_sibling\", \"tongjijiedian-\"), i = n(\"cm-remove_child\", \"shanchu2\"), r = n(\"cm-up\", \"rising\"), c = n(\"cm-down\", \"falling\"), l = n(\"cm-edit\", \"edit\"), h = document.createElement(\"ul\");\n  if (h.className = \"menu-list\", t && t.extend)\n    for (let u = 0; u < t.extend.length; u++) {\n      const g = t.extend[u], p = n(g.name, g.name);\n      h.appendChild(p), p.onclick = (m) => {\n        g.onclick(m);\n      };\n    }\n  const a = document.createElement(\"mmenu\");\n  a.className = \"mobile-menu\", a.appendChild(o), a.appendChild(s), a.appendChild(i), a.appendChild(r), a.appendChild(c), a.appendChild(l), a.hidden = !0, e.container.append(a);\n  let d = !0;\n  e.bus.addListener(\"unselectNode\", function() {\n    a.hidden = !0;\n  }), e.bus.addListener(\"selectNode\", function(u) {\n    a.hidden = !1, u.parent ? d = !1 : d = !0;\n  }), a.onclick = (u) => {\n    u.target === a && (a.hidden = !0);\n  }, o.onclick = () => {\n    e.addChild();\n  }, s.onclick = () => {\n    d || e.insertSibling(\"after\");\n  }, i.onclick = () => {\n    d || e.removeNode();\n  }, r.onclick = (u) => {\n    d || e.moveUpNode();\n  }, c.onclick = (u) => {\n    d || e.moveDownNode();\n  }, l.onclick = (u) => {\n    e.beginEdit();\n  };\n}\nconst de = document, _t = function(e, t) {\n  if (!t)\n    return he(e), e;\n  let n = e.querySelector(\".insert-preview\");\n  const o = `insert-preview ${t} show`;\n  return n || (n = de.createElement(\"div\"), e.appendChild(n)), n.className = o, e;\n}, he = function(e) {\n  if (!e)\n    return;\n  const t = e.querySelectorAll(\".insert-preview\");\n  for (const n of t || [])\n    n.remove();\n}, Ce = function(e, t) {\n  for (const n of t) {\n    const o = n.parentElement.parentElement.contains(e);\n    if (!(e && e.tagName === \"ME-TPC\" && e !== n && !o && e.nodeObj.parent))\n      return !1;\n  }\n  return !0;\n}, St = function(e) {\n  const t = document.createElement(\"div\");\n  return t.className = \"mind-elixir-ghost\", e.map.appendChild(t), t;\n};\nfunction Mt(e) {\n  let t = null, n = null, o = null;\n  const s = St(e), i = 12;\n  e.map.addEventListener(\"dragstart\", (r) => {\n    var l, h;\n    const c = r.target;\n    if ((c == null ? void 0 : c.tagName) !== \"ME-TPC\") {\n      r.preventDefault();\n      return;\n    }\n    (l = e.currentNodes) != null && l.includes(c) || (e.unselectNodes(), e.selectNode(c)), e.currentNodes ? (t = e.currentNodes, s.innerHTML = e.currentNodes.length + \" nodes\") : (t = [c], s.innerHTML = c.innerHTML);\n    for (const a of t)\n      a.parentElement.parentElement.style.opacity = \"0.5\";\n    (h = r.dataTransfer) == null || h.setDragImage(s, 0, 0), k.clear();\n  }), e.map.addEventListener(\"dragend\", async (r) => {\n    if (!t)\n      return;\n    for (const l of t)\n      l.parentElement.parentElement.style.opacity = \"1\";\n    const c = r.target;\n    c.style.opacity = \"\", o && (he(o), n === \"before\" ? e.moveNodeBefore(t, o) : n === \"after\" ? e.moveNodeAfter(t, o) : n === \"in\" && e.moveNodeIn(t, o), t = null);\n  }), e.map.addEventListener(\n    \"dragover\",\n    ot(function(r) {\n      if (!t)\n        return;\n      he(o);\n      const c = de.elementFromPoint(r.clientX, r.clientY - i);\n      if (Ce(c, t)) {\n        o = c;\n        const l = c.getBoundingClientRect().y;\n        r.clientY > l + c.clientHeight ? n = \"after\" : n = \"in\";\n      } else {\n        const l = de.elementFromPoint(r.clientX, r.clientY + i);\n        if (Ce(l, t)) {\n          o = l;\n          const h = l.getBoundingClientRect().y;\n          r.clientY < h ? n = \"before\" : n = \"in\";\n        } else\n          n = o = null;\n      }\n      o && _t(o, n);\n    }, 100)\n  );\n}\nconst kt = function(e) {\n  return [\"createSummary\", \"removeSummary\", \"finishEditSummary\"].includes(e.name) ? {\n    type: \"summary\",\n    value: e.obj.id\n  } : [\"createArrow\", \"removeArrow\", \"finishEditArrowLabel\"].includes(e.name) ? {\n    type: \"arrow\",\n    value: e.obj.id\n  } : [\"removeNodes\", \"copyNodes\", \"moveNodeBefore\", \"moveNodeAfter\", \"moveNodeIn\"].includes(e.name) ? {\n    type: \"nodes\",\n    value: e.objs.map((t) => t.id)\n  } : {\n    type: \"node\",\n    value: e.obj.id\n  };\n};\nfunction Lt(e) {\n  let t = [], n = -1, o = e.getData();\n  e.bus.addListener(\"operation\", (s) => {\n    if (s.name === \"beginEdit\")\n      return;\n    t = t.slice(0, n + 1);\n    const i = e.getData();\n    t.push({ prev: o, currentObject: kt(s), next: i }), o = i, n = t.length - 1;\n  }), e.undo = function() {\n    if (n > -1) {\n      const s = t[n];\n      o = s.prev, e.refresh(s.prev);\n      try {\n        s.currentObject.type === \"node\" ? e.selectNode(C(s.currentObject.value)) : s.currentObject.type === \"nodes\" && e.selectNodes(s.currentObject.value.map((i) => C(i)));\n      } catch {\n      } finally {\n        n--;\n      }\n    }\n  }, e.redo = function() {\n    if (n < t.length - 1) {\n      n++;\n      const s = t[n];\n      o = s.next, e.refresh(s.next), s.currentObject.type === \"node\" ? e.selectNode(C(s.currentObject.value)) : s.currentObject.type === \"nodes\" && e.selectNodes(s.currentObject.value.map((i) => C(i)));\n    }\n  }, e.map.addEventListener(\"keydown\", (s) => {\n    (s.metaKey || s.ctrlKey) && s.shiftKey && s.key === \"Z\" ? e.redo() : (s.metaKey || s.ctrlKey) && s.key === \"z\" && e.undo();\n  });\n}\nconst q = (e, t) => {\n  const n = document.createElement(\"span\");\n  return n.id = e, n.innerHTML = `<svg class=\"icon\" aria-hidden=\"true\">\n    <use xlink:href=\"#icon-${t}\"></use>\n  </svg>`, n;\n};\nfunction Tt(e) {\n  const t = document.createElement(\"div\"), n = q(\"fullscreen\", \"full\"), o = q(\"toCenter\", \"living\"), s = q(\"zoomout\", \"move\"), i = q(\"zoomin\", \"add\"), r = document.createElement(\"span\");\n  return r.innerText = \"100%\", t.appendChild(n), t.appendChild(o), t.appendChild(s), t.appendChild(i), t.className = \"mind-elixir-toolbar rb\", n.onclick = () => {\n    e.mindElixirBox.requestFullscreen();\n  }, o.onclick = () => {\n    e.toCenter();\n  }, s.onclick = () => {\n    e.scaleVal < 0.6 || e.scale(e.scaleVal -= 0.2);\n  }, i.onclick = () => {\n    e.scaleVal > 1.6 || e.scale(e.scaleVal += 0.2);\n  }, t;\n}\nfunction At(e) {\n  const t = document.createElement(\"div\"), n = q(\"tbltl\", \"left\"), o = q(\"tbltr\", \"right\"), s = q(\"tblts\", \"side\");\n  return t.appendChild(n), t.appendChild(o), t.appendChild(s), t.className = \"mind-elixir-toolbar lt\", n.onclick = () => {\n    e.initLeft();\n  }, o.onclick = () => {\n    e.initRight();\n  }, s.onclick = () => {\n    e.initSide();\n  }, t;\n}\nfunction jt(e) {\n  e.container.append(Tt(e)), e.container.append(At(e));\n}\n/*! @viselect/vanilla v3.6.0 MIT | https://github.com/Simonwep/selection/tree/master/packages/vanilla */\nvar Dt = Object.defineProperty, Ot = (e, t, n) => t in e ? Dt(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n, E = (e, t, n) => Ot(e, typeof t != \"symbol\" ? t + \"\" : t, n);\nclass $t {\n  constructor() {\n    E(this, \"_listeners\", /* @__PURE__ */ new Map()), E(this, \"on\", this.addEventListener), E(this, \"off\", this.removeEventListener), E(this, \"emit\", this.dispatchEvent);\n  }\n  addEventListener(t, n) {\n    const o = this._listeners.get(t) ?? /* @__PURE__ */ new Set();\n    return this._listeners.set(t, o), o.add(n), this;\n  }\n  removeEventListener(t, n) {\n    var o;\n    return (o = this._listeners.get(t)) == null || o.delete(n), this;\n  }\n  dispatchEvent(t, ...n) {\n    let o = !0;\n    for (const s of this._listeners.get(t) ?? [])\n      o = s(...n) !== !1 && o;\n    return o;\n  }\n  unbindAllListeners() {\n    this._listeners.clear();\n  }\n}\nconst _e = (e, t = \"px\") => typeof e == \"number\" ? e + t : e;\nfunction j({ style: e }, t, n) {\n  if (typeof t == \"object\")\n    for (const [o, s] of Object.entries(t))\n      s !== void 0 && (e[o] = _e(s));\n  else\n    n !== void 0 && (e[t] = _e(n));\n}\nconst Fe = (e) => (t, n, o, s = {}) => {\n  t instanceof HTMLCollection || t instanceof NodeList ? t = Array.from(t) : Array.isArray(t) || (t = [t]), Array.isArray(n) || (n = [n]);\n  for (const i of t)\n    if (i)\n      for (const r of n)\n        i[e](r, o, { capture: !1, ...s });\n  return [t, n, o, s];\n}, B = Fe(\"addEventListener\"), T = Fe(\"removeEventListener\"), ee = (e) => {\n  var t;\n  const { clientX: n, clientY: o, target: s } = ((t = e.touches) == null ? void 0 : t[0]) ?? e;\n  return { x: n, y: o, target: s };\n};\nfunction Se(e, t, n = \"touch\") {\n  switch (n) {\n    case \"center\": {\n      const o = t.left + t.width / 2, s = t.top + t.height / 2;\n      return o >= e.left && o <= e.right && s >= e.top && s <= e.bottom;\n    }\n    case \"cover\":\n      return t.left >= e.left && t.top >= e.top && t.right <= e.right && t.bottom <= e.bottom;\n    case \"touch\":\n      return e.right >= t.left && e.left <= t.right && e.bottom >= t.top && e.top <= t.bottom;\n  }\n}\nfunction F(e, t = document) {\n  const n = Array.isArray(e) ? e : [e];\n  let o = [];\n  for (let s = 0, i = n.length; s < i; s++) {\n    const r = n[s];\n    typeof r == \"string\" ? o = o.concat(Array.from(t.querySelectorAll(r))) : r instanceof Element && o.push(r);\n  }\n  return o;\n}\nconst Pt = () => matchMedia(\"(hover: none), (pointer: coarse)\").matches, Ht = () => \"safari\" in window, Bt = (e) => {\n  let t, n = -1, o = !1;\n  return {\n    next(...s) {\n      t = s, o || (o = !0, n = requestAnimationFrame(() => {\n        e(...t), o = !1;\n      }));\n    },\n    cancel() {\n      cancelAnimationFrame(n), o = !1;\n    }\n  };\n};\nfunction zt(e, t) {\n  for (const n of t) {\n    if (typeof n == \"number\")\n      return e.button === n;\n    if (typeof n == \"object\") {\n      const o = n.button === e.button, s = n.modifiers.every((i) => {\n        switch (i) {\n          case \"alt\":\n            return e.altKey;\n          case \"ctrl\":\n            return e.ctrlKey || e.metaKey;\n          case \"shift\":\n            return e.shiftKey;\n        }\n      });\n      return o && s;\n    }\n  }\n  return !1;\n}\nconst { abs: z, max: Me, min: ke, ceil: Le } = Math, Te = (e = []) => ({\n  stored: e,\n  selected: [],\n  touched: [],\n  changed: { added: [], removed: [] }\n});\nclass Ie extends $t {\n  constructor(t) {\n    var n, o, s, i, r;\n    super(), E(this, \"_options\"), E(this, \"_selection\", Te()), E(this, \"_area\"), E(this, \"_clippingElement\"), E(this, \"_targetElement\"), E(this, \"_targetBoundary\"), E(this, \"_targetBoundaryScrolled\", !0), E(this, \"_targetRect\"), E(this, \"_selectables\", []), E(this, \"_latestElement\"), E(this, \"_areaLocation\", { y1: 0, x2: 0, y2: 0, x1: 0 }), E(this, \"_areaRect\", new DOMRect()), E(this, \"_singleClick\", !0), E(this, \"_frame\"), E(this, \"_scrollAvailable\", !0), E(this, \"_scrollingActive\", !1), E(this, \"_scrollSpeed\", { x: 0, y: 0 }), E(this, \"_scrollDelta\", { x: 0, y: 0 }), E(this, \"disable\", this._toggleStartEvents.bind(this, !1)), E(this, \"enable\", this._toggleStartEvents), this._options = {\n      selectionAreaClass: \"selection-area\",\n      selectionContainerClass: void 0,\n      selectables: [],\n      document: window.document,\n      startAreas: [\"html\"],\n      boundaries: [\"html\"],\n      container: \"body\",\n      ...t,\n      behaviour: {\n        overlap: \"invert\",\n        intersect: \"touch\",\n        triggers: [0],\n        ...t.behaviour,\n        startThreshold: (n = t.behaviour) != null && n.startThreshold ? typeof t.behaviour.startThreshold == \"number\" ? t.behaviour.startThreshold : { x: 10, y: 10, ...t.behaviour.startThreshold } : { x: 10, y: 10 },\n        scrolling: {\n          speedDivider: 10,\n          manualSpeed: 750,\n          ...(o = t.behaviour) == null ? void 0 : o.scrolling,\n          startScrollMargins: {\n            x: 0,\n            y: 0,\n            ...(i = (s = t.behaviour) == null ? void 0 : s.scrolling) == null ? void 0 : i.startScrollMargins\n          }\n        }\n      },\n      features: {\n        range: !0,\n        touch: !0,\n        deselectOnBlur: !1,\n        ...t.features,\n        singleTap: {\n          allow: !0,\n          intersect: \"native\",\n          ...(r = t.features) == null ? void 0 : r.singleTap\n        }\n      }\n    };\n    for (const a of Object.getOwnPropertyNames(Object.getPrototypeOf(this)))\n      typeof this[a] == \"function\" && (this[a] = this[a].bind(this));\n    const { document: c, selectionAreaClass: l, selectionContainerClass: h } = this._options;\n    this._area = c.createElement(\"div\"), this._clippingElement = c.createElement(\"div\"), this._clippingElement.appendChild(this._area), this._area.classList.add(l), h && this._clippingElement.classList.add(h), j(this._area, {\n      willChange: \"top, left, bottom, right, width, height\",\n      top: 0,\n      left: 0,\n      position: \"fixed\"\n    }), j(this._clippingElement, {\n      overflow: \"hidden\",\n      position: \"fixed\",\n      transform: \"translate3d(0, 0, 0)\",\n      // https://stackoverflow.com/a/38268846\n      pointerEvents: \"none\",\n      zIndex: \"1\"\n    }), this._frame = Bt((a) => {\n      this._recalculateSelectionAreaRect(), this._updateElementSelection(), this._emitEvent(\"move\", a), this._redrawSelectionArea();\n    }), this.enable();\n  }\n  _toggleStartEvents(t = !0) {\n    const { document: n, features: o } = this._options, s = t ? B : T;\n    s(n, \"mousedown\", this._onTapStart), o.touch && s(n, \"touchstart\", this._onTapStart, { passive: !1 });\n  }\n  _onTapStart(t, n = !1) {\n    const { x: o, y: s, target: i } = ee(t), { document: r, startAreas: c, boundaries: l, features: h, behaviour: a } = this._options, d = i.getBoundingClientRect();\n    if (t instanceof MouseEvent && !zt(t, a.triggers))\n      return;\n    const u = F(c, r), g = F(l, r);\n    this._targetElement = g.find(\n      (v) => Se(v.getBoundingClientRect(), d)\n    );\n    const p = t.composedPath(), m = u.find((v) => p.includes(v));\n    if (this._targetBoundary = g.find((v) => p.includes(v)), !this._targetElement || !m || !this._targetBoundary || !n && this._emitEvent(\"beforestart\", t) === !1)\n      return;\n    this._areaLocation = { x1: o, y1: s, x2: 0, y2: 0 };\n    const f = r.scrollingElement ?? r.body;\n    this._scrollDelta = { x: f.scrollLeft, y: f.scrollTop }, this._singleClick = !0, this.clearSelection(!1, !0), B(r, [\"touchmove\", \"mousemove\"], this._delayedTapMove, { passive: !1 }), B(r, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop), B(r, \"scroll\", this._onScroll), h.deselectOnBlur && (this._targetBoundaryScrolled = !1, B(this._targetBoundary, \"scroll\", this._onStartAreaScroll));\n  }\n  _onSingleTap(t) {\n    const { singleTap: { intersect: n }, range: o } = this._options.features, s = ee(t);\n    let i;\n    if (n === \"native\")\n      i = s.target;\n    else if (n === \"touch\") {\n      this.resolveSelectables();\n      const { x: c, y: l } = s;\n      i = this._selectables.find((h) => {\n        const { right: a, left: d, top: u, bottom: g } = h.getBoundingClientRect();\n        return c < a && c > d && l < g && l > u;\n      });\n    }\n    if (!i)\n      return;\n    for (this.resolveSelectables(); !this._selectables.includes(i); )\n      if (i.parentElement)\n        i = i.parentElement;\n      else {\n        this._targetBoundaryScrolled || this.clearSelection();\n        return;\n      }\n    const { stored: r } = this._selection;\n    if (this._emitEvent(\"start\", t), t.shiftKey && o && this._latestElement) {\n      const c = this._latestElement, [l, h] = c.compareDocumentPosition(i) & 4 ? [i, c] : [c, i], a = [...this._selectables.filter(\n        (d) => d.compareDocumentPosition(l) & 4 && d.compareDocumentPosition(h) & 2\n      ), l, h];\n      this.select(a), this._latestElement = c;\n    } else\n      r.includes(i) && (r.length === 1 || t.ctrlKey || r.every((c) => this._selection.stored.includes(c))) ? this.deselect(i) : (this.select(i), this._latestElement = i);\n  }\n  _delayedTapMove(t) {\n    const { container: n, document: o, behaviour: { startThreshold: s } } = this._options, { x1: i, y1: r } = this._areaLocation, { x: c, y: l } = ee(t);\n    if (\n      // Single number for both coordinates\n      typeof s == \"number\" && z(c + l - (i + r)) >= s || // Different x and y threshold\n      typeof s == \"object\" && z(c - i) >= s.x || z(l - r) >= s.y\n    ) {\n      if (T(o, [\"mousemove\", \"touchmove\"], this._delayedTapMove, { passive: !1 }), this._emitEvent(\"beforedrag\", t) === !1) {\n        T(o, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop);\n        return;\n      }\n      B(o, [\"mousemove\", \"touchmove\"], this._onTapMove, { passive: !1 }), j(this._area, \"display\", \"block\"), F(n, o)[0].appendChild(this._clippingElement), this.resolveSelectables(), this._singleClick = !1, this._targetRect = this._targetElement.getBoundingClientRect(), this._scrollAvailable = this._targetElement.scrollHeight !== this._targetElement.clientHeight || this._targetElement.scrollWidth !== this._targetElement.clientWidth, this._scrollAvailable && (B(this._targetElement, \"wheel\", this._manualScroll, { passive: !1 }), this._selectables = this._selectables.filter((h) => this._targetElement.contains(h))), this._setupSelectionArea(), this._emitEvent(\"start\", t), this._onTapMove(t);\n    }\n    this._handleMoveEvent(t);\n  }\n  _setupSelectionArea() {\n    const { _clippingElement: t, _targetElement: n, _area: o } = this, s = this._targetRect = n.getBoundingClientRect();\n    this._scrollAvailable ? (j(t, {\n      top: s.top,\n      left: s.left,\n      width: s.width,\n      height: s.height\n    }), j(o, {\n      marginTop: -s.top,\n      marginLeft: -s.left\n    })) : (j(t, {\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: \"100%\"\n    }), j(o, {\n      marginTop: 0,\n      marginLeft: 0\n    }));\n  }\n  _onTapMove(t) {\n    const { _scrollSpeed: n, _areaLocation: o, _options: s, _frame: i } = this, { speedDivider: r } = s.behaviour.scrolling, c = this._targetElement, { x: l, y: h } = ee(t);\n    if (o.x2 = l, o.y2 = h, this._scrollAvailable && !this._scrollingActive && (n.y || n.x)) {\n      this._scrollingActive = !0;\n      const a = () => {\n        if (!n.x && !n.y) {\n          this._scrollingActive = !1;\n          return;\n        }\n        const { scrollTop: d, scrollLeft: u } = c;\n        n.y && (c.scrollTop += Le(n.y / r), o.y1 -= c.scrollTop - d), n.x && (c.scrollLeft += Le(n.x / r), o.x1 -= c.scrollLeft - u), i.next(t), requestAnimationFrame(a);\n      };\n      requestAnimationFrame(a);\n    } else\n      i.next(t);\n    this._handleMoveEvent(t);\n  }\n  _handleMoveEvent(t) {\n    const { features: n } = this._options;\n    (n.touch && Pt() || this._scrollAvailable && Ht()) && t.preventDefault();\n  }\n  _onScroll() {\n    const { _scrollDelta: t, _options: { document: n } } = this, { scrollTop: o, scrollLeft: s } = n.scrollingElement ?? n.body;\n    this._areaLocation.x1 += t.x - s, this._areaLocation.y1 += t.y - o, t.x = s, t.y = o, this._setupSelectionArea(), this._frame.next(null);\n  }\n  _onStartAreaScroll() {\n    this._targetBoundaryScrolled = !0, T(this._targetElement, \"scroll\", this._onStartAreaScroll);\n  }\n  _manualScroll(t) {\n    const { manualSpeed: n } = this._options.behaviour.scrolling, o = t.deltaY ? t.deltaY > 0 ? 1 : -1 : 0, s = t.deltaX ? t.deltaX > 0 ? 1 : -1 : 0;\n    this._scrollSpeed.y += o * n, this._scrollSpeed.x += s * n, this._onTapMove(t), t.preventDefault();\n  }\n  _recalculateSelectionAreaRect() {\n    const { _scrollSpeed: t, _areaLocation: n, _targetElement: o, _options: s } = this, { scrollTop: i, scrollHeight: r, clientHeight: c, scrollLeft: l, scrollWidth: h, clientWidth: a } = o, d = this._targetRect, { x1: u, y1: g } = n;\n    let { x2: p, y2: m } = n;\n    const { behaviour: { scrolling: { startScrollMargins: f } } } = s;\n    p < d.left + f.x ? (t.x = l ? -z(d.left - p + f.x) : 0, p = p < d.left ? d.left : p) : p > d.right - f.x ? (t.x = h - l - a ? z(d.left + d.width - p - f.x) : 0, p = p > d.right ? d.right : p) : t.x = 0, m < d.top + f.y ? (t.y = i ? -z(d.top - m + f.y) : 0, m = m < d.top ? d.top : m) : m > d.bottom - f.y ? (t.y = r - i - c ? z(d.top + d.height - m - f.y) : 0, m = m > d.bottom ? d.bottom : m) : t.y = 0;\n    const v = ke(u, p), b = ke(g, m), y = Me(u, p), x = Me(g, m);\n    this._areaRect = new DOMRect(v, b, y - v, x - b);\n  }\n  _redrawSelectionArea() {\n    const { x: t, y: n, width: o, height: s } = this._areaRect, { style: i } = this._area;\n    i.left = `${t}px`, i.top = `${n}px`, i.width = `${o}px`, i.height = `${s}px`;\n  }\n  _onTapStop(t, n) {\n    var o;\n    const { document: s, features: i } = this._options, { _singleClick: r } = this;\n    T(this._targetElement, \"scroll\", this._onStartAreaScroll), T(s, [\"mousemove\", \"touchmove\"], this._delayedTapMove), T(s, [\"touchmove\", \"mousemove\"], this._onTapMove), T(s, [\"mouseup\", \"touchcancel\", \"touchend\"], this._onTapStop), T(s, \"scroll\", this._onScroll), this._keepSelection(), t && r && i.singleTap.allow ? this._onSingleTap(t) : !r && !n && (this._updateElementSelection(), this._emitEvent(\"stop\", t)), this._scrollSpeed.x = 0, this._scrollSpeed.y = 0, T(this._targetElement, \"wheel\", this._manualScroll, { passive: !0 }), this._clippingElement.remove(), (o = this._frame) == null || o.cancel(), j(this._area, \"display\", \"none\");\n  }\n  _updateElementSelection() {\n    const { _selectables: t, _options: n, _selection: o, _areaRect: s } = this, { stored: i, selected: r, touched: c } = o, { intersect: l, overlap: h } = n.behaviour, a = h === \"invert\", d = [], u = [], g = [];\n    for (let m = 0; m < t.length; m++) {\n      const f = t[m];\n      if (Se(s, f.getBoundingClientRect(), l)) {\n        if (r.includes(f))\n          i.includes(f) && !c.includes(f) && c.push(f);\n        else if (a && i.includes(f)) {\n          g.push(f);\n          continue;\n        } else\n          u.push(f);\n        d.push(f);\n      }\n    }\n    a && u.push(...i.filter((m) => !r.includes(m)));\n    const p = h === \"keep\";\n    for (let m = 0; m < r.length; m++) {\n      const f = r[m];\n      !d.includes(f) && !// Check if the user wants to keep previously selected elements, e.g.,\n      // not make them part of the current selection as soon as they're touched.\n      (p && i.includes(f)) && g.push(f);\n    }\n    o.selected = d, o.changed = { added: u, removed: g }, this._latestElement = void 0;\n  }\n  _emitEvent(t, n) {\n    return this.emit(t, {\n      event: n,\n      store: this._selection,\n      selection: this\n    });\n  }\n  _keepSelection() {\n    const { _options: t, _selection: n } = this, { selected: o, changed: s, touched: i, stored: r } = n, c = o.filter((l) => !r.includes(l));\n    switch (t.behaviour.overlap) {\n      case \"drop\": {\n        n.stored = [\n          ...c,\n          ...r.filter((l) => !i.includes(l))\n          // Elements not touched\n        ];\n        break;\n      }\n      case \"invert\": {\n        n.stored = [\n          ...c,\n          ...r.filter((l) => !s.removed.includes(l))\n          // Elements not removed from selection\n        ];\n        break;\n      }\n      case \"keep\": {\n        n.stored = [\n          ...r,\n          ...o.filter((l) => !r.includes(l))\n          // Newly added\n        ];\n        break;\n      }\n    }\n  }\n  /**\n   * Manually triggers the start of a selection\n   * @param evt A MouseEvent / TouchEvent-like object\n   * @param silent If beforestart should be fired,\n   */\n  trigger(t, n = !0) {\n    this._onTapStart(t, n);\n  }\n  /**\n   * Can be used if during a selection elements have been added.\n   * Will update everything that can be selected.\n   */\n  resolveSelectables() {\n    this._selectables = F(this._options.selectables, this._options.document);\n  }\n  /**\n   * Same as deselecting, but for all elements currently selected.\n   * @param includeStored If the store should also get cleared\n   * @param quiet If move / stop events should be fired\n   */\n  clearSelection(t = !0, n = !1) {\n    const { selected: o, stored: s, changed: i } = this._selection;\n    i.added = [], i.removed.push(\n      ...o,\n      ...t ? s : []\n    ), n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)), this._selection = Te(t ? [] : s);\n  }\n  /**\n   * @returns {Array} Selected elements\n   */\n  getSelection() {\n    return this._selection.stored;\n  }\n  /**\n   * @returns {HTMLElement} The selection area element\n   */\n  getSelectionArea() {\n    return this._area;\n  }\n  /**\n   * Cancel the current selection process, pass true to fire a stop event after cancel.\n   */\n  cancel(t = !1) {\n    this._onTapStop(null, !t);\n  }\n  /**\n   * Unbinds all events and removes the area-element.\n   */\n  destroy() {\n    this.cancel(), this.disable(), this._clippingElement.remove(), super.unbindAllListeners();\n  }\n  /**\n   * Adds elements to the selection\n   * @param query - CSS Query, can be an array of queries\n   * @param quiet - If this should not trigger the move event\n   */\n  select(t, n = !1) {\n    const { changed: o, selected: s, stored: i } = this._selection, r = F(t, this._options.document).filter(\n      (c) => !s.includes(c) && !i.includes(c)\n    );\n    return i.push(...r), s.push(...r), o.added.push(...r), o.removed = [], this._latestElement = void 0, n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)), r;\n  }\n  /**\n   * Removes a particular element from the selection.\n   * @param query - CSS Query, can be an array of queries\n   * @param quiet - If this should not trigger the move event\n   */\n  deselect(t, n = !1) {\n    const { selected: o, stored: s, changed: i } = this._selection, r = F(t, this._options.document).filter(\n      (c) => o.includes(c) || s.includes(c)\n    );\n    r.length && (this._selection.stored = s.filter((c) => !r.includes(c)), this._selection.selected = o.filter((c) => !r.includes(c)), this._selection.changed.added = [], this._selection.changed.removed.push(\n      ...r.filter((c) => !i.removed.includes(c))\n    ), this._latestElement = void 0, n || (this._emitEvent(\"move\", null), this._emitEvent(\"stop\", null)));\n  }\n}\nE(Ie, \"version\", \"3.6.0\");\nfunction Rt(e) {\n  const t = new Ie({\n    selectables: [\".map-container me-tpc\"],\n    boundaries: [e.container],\n    container: \"body\",\n    behaviour: {\n      // Scroll configuration.\n      scrolling: {\n        // On scrollable areas the number on px per frame is devided by this amount.\n        // Default is 10 to provide a enjoyable scroll experience.\n        speedDivider: 10,\n        // Browsers handle mouse-wheel events differently, this number will be used as\n        // numerator to calculate the mount of px while scrolling manually: manualScrollSpeed / scrollSpeedDivider.\n        manualSpeed: 750,\n        // This property defines the virtual inset margins from the borders of the container\n        // component that, when crossed by the mouse/touch, trigger the scrolling. Useful for\n        // fullscreen containers.\n        startScrollMargins: { x: 10, y: 10 }\n      }\n    }\n  }).on(\"beforestart\", ({ event: n }) => {\n    if (n.button !== e.mouseSelectionButton || n.target.tagName === \"ME-TPC\" || n.target.id === \"input-box\" || n.target.className === \"circle\")\n      return !1;\n    const o = t.getSelectionArea();\n    return o.style.background = \"#4f90f22d\", o.style.border = \"1px solid #4f90f2\", o.parentElement && (o.parentElement.style.zIndex = \"9999\"), !0;\n  }).on(\"start\", ({ event: n }) => {\n    !n.ctrlKey && !n.metaKey && (e.clearSelection(), t.clearSelection(!0, !0));\n  }).on(\n    \"move\",\n    ({\n      store: {\n        changed: { added: n, removed: o }\n      }\n    }) => {\n      k.moved = !0;\n      for (const s of n)\n        s.classList.add(\"selected\");\n      for (const s of o)\n        s.classList.remove(\"selected\");\n    }\n  ).on(\"stop\", ({ store: { stored: n } }) => {\n    e.selectNodes(n);\n  });\n  e.selection = t;\n}\nconst qt = function(e, t = !0) {\n  this.theme = e;\n  const n = this.theme.cssVar, o = Object.keys(n);\n  for (let s = 0; s < o.length; s++) {\n    const i = o[s];\n    this.mindElixirBox.style.setProperty(i, n[i]);\n  }\n  t && this.refresh();\n}, W = (e) => {\n  var o;\n  const t = (o = e.parent) == null ? void 0 : o.children, n = (t == null ? void 0 : t.indexOf(e)) ?? 0;\n  return { siblings: t, index: n };\n};\nfunction Ft(e) {\n  const { siblings: t, index: n } = W(e);\n  if (t === void 0)\n    return;\n  const o = t[n];\n  n === 0 ? (t[n] = t[t.length - 1], t[t.length - 1] = o) : (t[n] = t[n - 1], t[n - 1] = o);\n}\nfunction It(e) {\n  const { siblings: t, index: n } = W(e);\n  if (t === void 0)\n    return;\n  const o = t[n];\n  n === t.length - 1 ? (t[n] = t[0], t[0] = o) : (t[n] = t[n + 1], t[n + 1] = o);\n}\nfunction ge(e) {\n  const { siblings: t, index: n } = W(e);\n  return t === void 0 ? 0 : (t.splice(n, 1), t.length);\n}\nfunction Kt(e, t, n) {\n  const { siblings: o, index: s } = W(n);\n  o !== void 0 && (t === \"before\" ? o.splice(s, 0, e) : o.splice(s + 1, 0, e));\n}\nfunction Vt(e, t) {\n  const { siblings: n, index: o } = W(e);\n  n !== void 0 && (n[o] = t, t.children = [e]);\n}\nfunction Ke(e, t, n) {\n  if (ge(t), e === \"in\")\n    n.children ? n.children.push(t) : n.children = [t];\n  else {\n    t.direction !== void 0 && (t.direction = n.direction);\n    const { siblings: o, index: s } = W(n);\n    if (o === void 0)\n      return;\n    e === \"before\" ? o.splice(s, 0, t) : o.splice(s + 1, 0, t);\n  }\n}\nconst Wt = function(e, t) {\n  var n, o;\n  if (e === L)\n    return L;\n  if (e === R)\n    return R;\n  if (e === ce) {\n    const s = ((n = document.querySelector(\".lhs\")) == null ? void 0 : n.childElementCount) || 0, i = ((o = document.querySelector(\".rhs\")) == null ? void 0 : o.childElementCount) || 0;\n    return s <= i ? (t.direction = L, L) : (t.direction = R, R);\n  }\n}, Ve = function(e, t, n) {\n  var i, r;\n  const o = n.children[0].children[0], s = t.parentElement;\n  if (s.tagName === \"ME-PARENT\") {\n    if (J(o), s.children[1])\n      s.nextSibling.appendChild(n);\n    else {\n      const c = e.createChildren([n]);\n      s.appendChild(me(!0)), s.insertAdjacentElement(\"afterend\", c);\n    }\n    e.linkDiv(n.offsetParent);\n  } else\n    s.tagName === \"ME-ROOT\" && (Wt(e.direction, o.nodeObj) === L ? (i = e.container.querySelector(\".lhs\")) == null || i.appendChild(n) : (r = e.container.querySelector(\".rhs\")) == null || r.appendChild(n), e.linkDiv());\n}, We = function(e, t) {\n  const n = e.parentNode;\n  if (t === 0) {\n    const o = n.parentNode.parentNode;\n    o.tagName !== \"ME-MAIN\" && o.previousSibling.children[1].remove();\n  }\n  n.parentNode.remove();\n}, Ue = {\n  before: \"beforebegin\",\n  after: \"afterend\"\n}, J = function(e) {\n  const n = e.parentElement.parentElement.lastElementChild;\n  (n == null ? void 0 : n.tagName) === \"svg\" && (n == null || n.remove());\n}, Ut = function(e, t) {\n  const n = e.nodeObj, o = fe(n);\n  o.style && t.style && (t.style = Object.assign(o.style, t.style));\n  const s = Object.assign(n, t);\n  pe(e, s), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"reshapeNode\",\n    obj: s,\n    origin: o\n  });\n}, ve = function(e, t, n) {\n  if (!t)\n    return null;\n  const o = t.nodeObj;\n  o.expanded === !1 && (e.expandNode(t, !0), t = C(o.id));\n  const s = n || e.generateNewObj();\n  o.children ? o.children.push(s) : o.children = [s], O(e.nodeData);\n  const { grp: i, top: r } = e.createWrapper(s);\n  return Ve(e, t, i), { newTop: r, newNodeObj: s };\n}, Yt = function(e, t, n) {\n  var h, a, d;\n  const o = t || this.currentNode;\n  if (!o)\n    return;\n  const s = o.nodeObj;\n  if (s.parent) {\n    if (!((h = s.parent) != null && h.parent) && ((d = (a = s.parent) == null ? void 0 : a.children) == null ? void 0 : d.length) === 1) {\n      this.addChild(C(s.parent.id), n);\n      return;\n    }\n  } else {\n    this.addChild();\n    return;\n  }\n  const i = n || this.generateNewObj();\n  Kt(i, e, s), O(this.nodeData);\n  const r = o.parentElement, { grp: c, top: l } = this.createWrapper(i);\n  r.parentElement.insertAdjacentElement(Ue[e], c), this.linkDiv(c.offsetParent), n || this.editTopic(l.firstChild), this.selectNode(l.firstChild, !0), this.bus.fire(\"operation\", {\n    name: \"insertSibling\",\n    type: e,\n    obj: i\n  });\n}, Xt = function(e, t) {\n  const n = e || this.currentNode;\n  if (!n)\n    return;\n  J(n);\n  const o = n.nodeObj;\n  if (!o.parent)\n    return;\n  const s = t || this.generateNewObj();\n  Vt(o, s), O(this.nodeData);\n  const i = n.parentElement.parentElement, { grp: r, top: c } = this.createWrapper(s, !0);\n  c.appendChild(me(!0)), i.insertAdjacentElement(\"afterend\", r);\n  const l = this.createChildren([i]);\n  c.insertAdjacentElement(\"afterend\", l), this.linkDiv(), t || this.editTopic(c.firstChild), this.selectNode(c.firstChild, !0), this.bus.fire(\"operation\", {\n    name: \"insertParent\",\n    obj: s\n  });\n}, Gt = function(e, t) {\n  const n = e || this.currentNode;\n  if (!n)\n    return;\n  const o = ve(this, n, t);\n  if (!o)\n    return;\n  const { newTop: s, newNodeObj: i } = o;\n  this.bus.fire(\"operation\", {\n    name: \"addChild\",\n    obj: i\n  }), t || this.editTopic(s.firstChild), this.selectNode(s.firstChild, !0);\n}, Jt = function(e, t) {\n  const n = fe(e.nodeObj);\n  ue(n);\n  const o = ve(this, t, n);\n  if (!o)\n    return;\n  const { newNodeObj: s } = o;\n  this.selectNode(C(s.id)), this.bus.fire(\"operation\", {\n    name: \"copyNode\",\n    obj: s\n  });\n}, Zt = function(e, t) {\n  e = le(e);\n  const n = [];\n  for (let o = 0; o < e.length; o++) {\n    const s = e[o], i = fe(s.nodeObj);\n    ue(i);\n    const r = ve(this, t, i);\n    if (!r)\n      return;\n    const { newNodeObj: c } = r;\n    n.push(c);\n  }\n  this.selectNodes(n.map((o) => C(o.id))), this.bus.fire(\"operation\", {\n    name: \"copyNodes\",\n    objs: n\n  });\n}, Qt = function(e) {\n  const t = e || this.currentNode;\n  if (!t)\n    return;\n  const n = t.nodeObj;\n  Ft(n);\n  const o = t.parentNode.parentNode;\n  o.parentNode.insertBefore(o, o.previousSibling), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveUpNode\",\n    obj: n\n  });\n}, en = function(e) {\n  const t = e || this.currentNode;\n  if (!t)\n    return;\n  const n = t.nodeObj;\n  It(n);\n  const o = t.parentNode.parentNode;\n  o.nextSibling ? o.nextSibling.insertAdjacentElement(\"afterend\", o) : o.parentNode.prepend(o), this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveDownNode\",\n    obj: n\n  });\n}, tn = function(e) {\n  var r;\n  const t = e || this.currentNode;\n  if (!t)\n    return;\n  const n = t.nodeObj;\n  if (!n.parent)\n    throw new Error(\"Can not remove root node\");\n  const o = n.parent.children, s = o.findIndex((c) => c === n), i = ge(n);\n  if (We(t, i), o.length !== 0) {\n    const c = o[s] || o[s - 1];\n    this.selectNode(C(c.id));\n  } else\n    this.selectNode(C(n.parent.id));\n  this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"removeNode\",\n    obj: n,\n    originIndex: s,\n    originParentId: (r = n == null ? void 0 : n.parent) == null ? void 0 : r.id\n  });\n}, nn = function(e) {\n  e = le(e);\n  for (const t of e) {\n    const n = t.nodeObj;\n    if (!n.parent)\n      continue;\n    const o = ge(n);\n    We(t, o);\n  }\n  this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"removeNodes\",\n    objs: e.map((t) => t.nodeObj)\n  });\n}, on = function(e, t) {\n  e = le(e);\n  const n = t.nodeObj;\n  n.expanded === !1 && (this.expandNode(t, !0), t = C(n.id));\n  for (const o of e) {\n    const s = o.nodeObj;\n    Ke(\"in\", s, n), O(this.nodeData);\n    const i = o.parentElement;\n    Ve(this, t, i.parentElement);\n  }\n  this.linkDiv(), this.bus.fire(\"operation\", {\n    name: \"moveNodeIn\",\n    objs: e.map((o) => o.nodeObj),\n    toObj: n\n  });\n}, Ye = (e, t, n, o) => {\n  e = le(e), t === \"after\" && (e = e.reverse());\n  const s = n.nodeObj;\n  for (const i of e) {\n    const r = i.nodeObj;\n    Ke(t, r, s), O(o.nodeData), J(i);\n    const c = i.parentElement.parentNode;\n    n.parentElement.parentNode.insertAdjacentElement(Ue[t], c);\n  }\n  o.linkDiv(), o.bus.fire(\"operation\", {\n    name: t === \"before\" ? \"moveNodeBefore\" : \"moveNodeAfter\",\n    objs: e.map((i) => i.nodeObj),\n    toObj: s\n  });\n}, sn = function(e, t) {\n  Ye(e, \"before\", t, this);\n}, rn = function(e, t) {\n  Ye(e, \"after\", t, this);\n}, cn = function(e) {\n  const t = e || this.currentNode;\n  t && (t.nodeObj.dangerouslySetInnerHTML || this.editTopic(t));\n}, ln = function(e, t) {\n  e.text.textContent = t, e.nodeObj.topic = t, this.linkDiv();\n}, Xe = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  addChild: Gt,\n  beginEdit: cn,\n  copyNode: Jt,\n  copyNodes: Zt,\n  insertParent: Xt,\n  insertSibling: Yt,\n  moveDownNode: en,\n  moveNodeAfter: rn,\n  moveNodeBefore: sn,\n  moveNodeIn: on,\n  moveUpNode: Qt,\n  removeNode: tn,\n  removeNodes: nn,\n  reshapeNode: Ut,\n  rmSubline: J,\n  setNodeTopic: ln\n}, Symbol.toStringTag, { value: \"Module\" }));\nfunction Ge(e) {\n  return {\n    nodeData: e.isFocusMode ? e.nodeDataBackup : e.nodeData,\n    arrows: e.arrows,\n    summaries: e.summaries,\n    direction: e.direction,\n    theme: e.theme\n  };\n}\nconst an = function(e, t, n) {\n  if (e) {\n    if (this.clearSelection(), typeof e == \"string\") {\n      const o = C(e);\n      return o ? this.selectNode(o) : void 0;\n    }\n    e.className = \"selected\", e.scrollIntoView({ block: \"nearest\", inline: \"nearest\" }), this.currentNode = e, t ? this.bus.fire(\"selectNewNode\", e.nodeObj) : this.bus.fire(\"selectNode\", e.nodeObj, n);\n  }\n}, dn = function() {\n  this.currentNode && (this.currentNode.className = \"\"), this.currentNode = null, this.bus.fire(\"unselectNode\");\n}, hn = function(e) {\n  this.clearSelection();\n  for (const t of e)\n    t.className = \"selected\";\n  this.currentNodes = e, this.bus.fire(\n    \"selectNodes\",\n    e.map((t) => t.nodeObj)\n  );\n}, un = function() {\n  if (this.currentNodes)\n    for (const e of this.currentNodes)\n      e.classList.remove(\"selected\");\n  this.currentNodes = null, this.bus.fire(\"unselectNodes\");\n}, fn = function() {\n  this.unselectNode(), this.unselectNodes(), this.unselectSummary(), this.unselectArrow();\n}, pn = function() {\n  const e = Ge(this);\n  return JSON.stringify(e, (t, n) => {\n    if (!(t === \"parent\" && typeof n != \"string\"))\n      return n;\n  });\n}, mn = function() {\n  return JSON.parse(this.getDataString());\n}, gn = function() {\n  const e = Ge(this).nodeData;\n  let t = \"# \" + e.topic + `\n\n`;\n  function n(o, s) {\n    for (let i = 0; i < o.length; i++)\n      s <= 6 ? t += \"\".padStart(s, \"#\") + \" \" + o[i].topic + `\n\n` : t += \"\".padStart(s - 7, \"\t\") + \"- \" + o[i].topic + `\n`, o[i].children && n(o[i].children || [], s + 1);\n  }\n  return n(e.children || [], 2), t;\n}, vn = function() {\n  this.editable = !0;\n}, bn = function() {\n  this.editable = !1;\n}, yn = function(e) {\n  this.scaleVal = e, this.map.style.transform = \"scale(\" + e + \")\";\n}, xn = function() {\n  this.container.scrollTo(1e4 - this.container.offsetWidth / 2, 1e4 - this.container.offsetHeight / 2);\n}, wn = function(e) {\n  e(this);\n}, En = function(e) {\n  e.nodeObj.parent && (this.tempDirection === null && (this.tempDirection = this.direction), this.isFocusMode || (this.nodeDataBackup = this.nodeData, this.isFocusMode = !0), this.nodeData = e.nodeObj, this.initRight(), this.toCenter());\n}, Nn = function() {\n  this.isFocusMode = !1, this.tempDirection !== null && (this.nodeData = this.nodeDataBackup, this.direction = this.tempDirection, this.tempDirection = null, this.refresh(), this.toCenter());\n}, Cn = function() {\n  this.direction = 0, this.refresh();\n}, _n = function() {\n  this.direction = 1, this.refresh();\n}, Sn = function() {\n  this.direction = 2, this.refresh();\n}, Mn = function(e) {\n  this.locale = e, this.refresh();\n}, kn = function(e, t) {\n  const n = e.nodeObj;\n  typeof t == \"boolean\" ? n.expanded = t : n.expanded !== !1 ? n.expanded = !1 : n.expanded = !0;\n  const o = e.parentNode, s = o.children[1];\n  if (s.expanded = n.expanded, s.className = n.expanded ? \"minus\" : \"\", J(e), n.expanded) {\n    const i = this.createChildren(\n      n.children.map((r) => this.createWrapper(r).grp)\n    );\n    o.parentNode.appendChild(i);\n  } else\n    o.parentNode.children[1].remove();\n  this.linkDiv(), this.bus.fire(\"expandNode\", n);\n}, Ln = function(e) {\n  e && (e = JSON.parse(JSON.stringify(e)), this.nodeData = e.nodeData, this.arrows = e.arrows || [], this.summaries = e.summaries || []), O(this.nodeData), this.layout(), this.linkDiv();\n}, Tn = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  cancelFocus: Nn,\n  clearSelection: fn,\n  disableEdit: bn,\n  enableEdit: vn,\n  expandNode: kn,\n  focusNode: En,\n  getData: mn,\n  getDataMd: gn,\n  getDataString: pn,\n  initLeft: Cn,\n  initRight: _n,\n  initSide: Sn,\n  install: wn,\n  refresh: Ln,\n  scale: yn,\n  selectNode: an,\n  selectNodes: hn,\n  setLocale: Mn,\n  toCenter: xn,\n  unselectNode: dn,\n  unselectNodes: un\n}, Symbol.toStringTag, { value: \"Module\" })), An = function(e) {\n  return {\n    dom: e,\n    moved: !1,\n    // diffrentiate click and move\n    mousedown: !1,\n    handleMouseMove(t) {\n      this.mousedown && (this.moved = !0, this.cb && this.cb(t.movementX, t.movementY));\n    },\n    handleMouseDown(t) {\n      t.button === 0 && (this.mousedown = !0);\n    },\n    handleClear(t) {\n      this.mousedown = !1;\n    },\n    cb: null,\n    init(t, n) {\n      this.cb = n, this.handleClear = this.handleClear.bind(this), this.handleMouseMove = this.handleMouseMove.bind(this), this.handleMouseDown = this.handleMouseDown.bind(this), t.addEventListener(\"mousemove\", this.handleMouseMove), t.addEventListener(\"mouseleave\", this.handleClear), t.addEventListener(\"mouseup\", this.handleClear), this.dom.addEventListener(\"mousedown\", this.handleMouseDown);\n    },\n    destory(t) {\n      t.removeEventListener(\"mousemove\", this.handleMouseMove), t.removeEventListener(\"mouseleave\", this.handleClear), t.removeEventListener(\"mouseup\", this.handleClear), this.dom.removeEventListener(\"mousedown\", this.handleMouseDown);\n    },\n    clear() {\n      this.moved = !1, this.mousedown = !1;\n    }\n  };\n}, Ae = {\n  create: An\n};\nfunction re(e, t, n) {\n  const { offsetLeft: o, offsetTop: s } = $(e.nodes, t), i = t.offsetWidth, r = t.offsetHeight, c = o + i / 2, l = s + r / 2, h = c + n.x, a = l + n.y;\n  return {\n    w: i,\n    h: r,\n    cx: c,\n    cy: l,\n    ctrlX: h,\n    ctrlY: a\n  };\n}\nfunction I(e) {\n  let t, n;\n  const o = (e.cy - e.ctrlY) / (e.ctrlX - e.cx);\n  return o > e.h / e.w || o < -e.h / e.w ? e.cy - e.ctrlY < 0 ? (t = e.cx - e.h / 2 / o, n = e.cy + e.h / 2) : (t = e.cx + e.h / 2 / o, n = e.cy - e.h / 2) : e.cx - e.ctrlX < 0 ? (t = e.cx + e.w / 2, n = e.cy - e.w * o / 2) : (t = e.cx - e.w / 2, n = e.cy + e.w * o / 2), {\n    x: t,\n    y: n\n  };\n}\nconst jn = function(e, t, n, o) {\n  const s = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  return N(s, {\n    \"text-anchor\": \"middle\",\n    x: t + \"\",\n    y: n + \"\",\n    fill: o || \"#666\"\n  }), s.dataset.type = \"custom-link\", s.innerHTML = e, s;\n}, Je = function(e, t, n, o, s) {\n  if (!t || !n)\n    return;\n  performance.now();\n  const i = re(e, t, o.delta1), r = re(e, n, o.delta2), { x: c, y: l } = I(i), { ctrlX: h, ctrlY: a } = i, { ctrlX: d, ctrlY: u } = r, { x: g, y: p } = I(r), m = He(d, u, g, p), f = mt(\n    `M ${c} ${l} C ${h} ${a} ${d} ${u} ${g} ${p}`,\n    `M ${m.x1} ${m.y1} L ${g} ${p} L ${m.x2} ${m.y2}`\n  ), v = c / 8 + h * 3 / 8 + d * 3 / 8 + g / 8, b = l / 8 + a * 3 / 8 + u * 3 / 8 + p / 8, y = jn(o.label, v, b, e.theme.cssVar[\"--color\"]);\n  f.appendChild(y), f.arrowObj = o, f.dataset.linkid = o.id, e.linkSvgGroup.appendChild(f), s || (e.arrows.push(o), e.currentArrow = f, Qe(e, o, i, r)), performance.now();\n}, Dn = function(e, t) {\n  const n = {\n    id: G(),\n    label: \"Custom Link\",\n    from: e.nodeObj.id,\n    to: t.nodeObj.id,\n    delta1: {\n      x: 0,\n      y: -200\n    },\n    delta2: {\n      x: 0,\n      y: -200\n    }\n  };\n  Je(this, e, t, n), this.bus.fire(\"operation\", {\n    name: \"createArrow\",\n    obj: n\n  });\n}, On = function(e) {\n  let t;\n  if (e ? t = e : t = this.currentArrow, !t)\n    return;\n  Ze(this);\n  const n = t.arrowObj.id;\n  this.arrows = this.arrows.filter((o) => o.id !== n), t.remove(), this.bus.fire(\"operation\", {\n    name: \"removeArrow\",\n    obj: {\n      id: n\n    }\n  });\n}, $n = function(e) {\n  this.currentArrow = e;\n  const t = e.arrowObj, n = C(t.from), o = C(t.to), s = re(this, n, t.delta1), i = re(this, o, t.delta2);\n  Qe(this, t, s, i);\n}, Pn = function() {\n  this.currentArrow = null, Ze(this);\n}, Ze = function(e) {\n  e.linkController.style.display = \"none\", e.P2.style.display = \"none\", e.P3.style.display = \"none\";\n}, Qe = function(e, t, n, o) {\n  var u;\n  e.linkController.style.display = \"initial\", e.P2.style.display = \"initial\", e.P3.style.display = \"initial\", e.nodes.appendChild(e.linkController), e.nodes.appendChild(e.P2), e.nodes.appendChild(e.P3);\n  let { x: s, y: i } = I(n), { ctrlX: r, ctrlY: c } = n, { ctrlX: l, ctrlY: h } = o, { x: a, y: d } = I(o);\n  e.P2.style.cssText = `top:${c}px;left:${r}px;`, e.P3.style.cssText = `top:${h}px;left:${l}px;`, N(e.line1, {\n    x1: s + \"\",\n    y1: i + \"\",\n    x2: r + \"\",\n    y2: c + \"\"\n  }), N(e.line2, {\n    x1: l + \"\",\n    y1: h + \"\",\n    x2: a + \"\",\n    y2: d + \"\"\n  }), e.helper1 && (e.helper1.destory(e.map), (u = e.helper2) == null || u.destory(e.map)), e.helper1 = Ae.create(e.P2), e.helper2 = Ae.create(e.P3), e.helper1.init(e.map, (g, p) => {\n    var b;\n    r = r + g / e.scaleVal, c = c + p / e.scaleVal;\n    const m = I({ ...n, ctrlX: r, ctrlY: c });\n    s = m.x, i = m.y;\n    const f = s / 8 + r * 3 / 8 + l * 3 / 8 + a / 8, v = i / 8 + c * 3 / 8 + h * 3 / 8 + d / 8;\n    e.P2.style.top = c + \"px\", e.P2.style.left = r + \"px\", (b = e.currentArrow) == null || b.children[0].setAttribute(\"d\", `M ${s} ${i} C ${r} ${c} ${l} ${h} ${a} ${d}`), N(e.currentArrow.children[2], {\n      x: f + \"\",\n      y: v + \"\"\n    }), N(e.line1, {\n      x1: s + \"\",\n      y1: i + \"\",\n      x2: r + \"\",\n      y2: c + \"\"\n    }), t.delta1.x = r - n.cx, t.delta1.y = c - n.cy;\n  }), e.helper2.init(e.map, (g, p) => {\n    var y, x;\n    l = l + g / e.scaleVal, h = h + p / e.scaleVal;\n    const m = I({ ...o, ctrlX: l, ctrlY: h });\n    a = m.x, d = m.y;\n    const f = s / 8 + r * 3 / 8 + l * 3 / 8 + a / 8, v = i / 8 + c * 3 / 8 + h * 3 / 8 + d / 8, b = He(l, h, a, d);\n    e.P3.style.top = h + \"px\", e.P3.style.left = l + \"px\", (y = e.currentArrow) == null || y.children[0].setAttribute(\"d\", `M ${s} ${i} C ${r} ${c} ${l} ${h} ${a} ${d}`), (x = e.currentArrow) == null || x.children[1].setAttribute(\"d\", `M ${b.x1} ${b.y1} L ${a} ${d} L ${b.x2} ${b.y2}`), N(e.currentArrow.children[2], {\n      x: f + \"\",\n      y: v + \"\"\n    }), N(e.line2, {\n      x1: l + \"\",\n      y1: h + \"\",\n      x2: a + \"\",\n      y2: d + \"\"\n    }), t.delta2.x = l - o.cx, t.delta2.y = h - o.cy;\n  });\n};\nfunction Hn() {\n  this.linkSvgGroup.innerHTML = \"\";\n  for (let e = 0; e < this.arrows.length; e++) {\n    const t = this.arrows[e];\n    try {\n      Je(this, C(t.from), C(t.to), t, !0);\n    } catch {\n    }\n  }\n  this.nodes.appendChild(this.linkSvgGroup);\n}\nfunction Bn(e) {\n  if (!e)\n    return;\n  const t = e.children[2];\n  Re(this, t, (n) => {\n    var i;\n    const o = e.arrowObj, s = ((i = n.textContent) == null ? void 0 : i.trim()) || \"\";\n    s === \"\" ? o.label = origin : o.label = s, n.remove(), s !== origin && (t.innerHTML = o.label, this.linkDiv(), this.bus.fire(\"operation\", {\n      name: \"finishEditArrowLabel\",\n      obj: o\n    }));\n  });\n}\nfunction zn() {\n  this.arrows = this.arrows.filter((e) => se(e.from, this.nodeData) && se(e.to, this.nodeData));\n}\nconst Rn = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  createArrow: Dn,\n  editArrowLabel: Bn,\n  removeArrow: On,\n  renderArrow: Hn,\n  selectArrow: $n,\n  tidyArrow: zn,\n  unselectArrow: Pn\n}, Symbol.toStringTag, { value: \"Module\" })), qn = function(e) {\n  var l, h;\n  if (e.length === 0)\n    throw new Error(\"No selected node.\");\n  if (e.length === 1) {\n    const a = e[0].nodeObj, d = e[0].nodeObj.parent;\n    if (!d)\n      throw new Error(\"Can not select root node.\");\n    const u = d.children.findIndex((g) => a === g);\n    return {\n      parent: d.id,\n      start: u,\n      end: u\n    };\n  }\n  let t = 0;\n  const n = e.map((a) => {\n    let d = a.nodeObj;\n    const u = [];\n    for (; d.parent; ) {\n      const g = d.parent, p = g.children, m = p == null ? void 0 : p.indexOf(d);\n      d = g, u.unshift({ node: d, index: m });\n    }\n    return u.length > t && (t = u.length), u;\n  });\n  let o = 0;\n  e:\n    for (; o < t; o++) {\n      const a = (l = n[0][o]) == null ? void 0 : l.node;\n      for (let d = 1; d < n.length; d++)\n        if (((h = n[d][o]) == null ? void 0 : h.node) !== a)\n          break e;\n    }\n  if (!o)\n    throw new Error(\"Can not select root node.\");\n  const s = n.map((a) => a[o - 1].index).sort(), i = s[0] || 0, r = s[s.length - 1] || 0, c = n[0][o - 1].node;\n  if (!c.parent)\n    throw new Error(\"Please select nodes in the same main topic.\");\n  return {\n    parent: c.id,\n    start: i,\n    end: r\n  };\n}, Fn = function(e) {\n  const t = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  return t.setAttribute(\"id\", e), t;\n}, je = function(e, t) {\n  const n = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  return N(n, {\n    d: e,\n    stroke: t || \"#666\",\n    fill: \"none\",\n    \"stroke-linecap\": \"round\",\n    \"stroke-width\": \"2\"\n  }), n;\n}, De = function(e, t, n, o, s) {\n  const i = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  return N(i, {\n    \"text-anchor\": o,\n    x: t + \"\",\n    y: n + \"\",\n    fill: s || \"#666\"\n  }), i.innerHTML = e, i;\n}, In = (e) => C(e).parentElement.parentElement, Kn = function({ parent: e, start: t }) {\n  var i, r;\n  const n = C(e), o = n.nodeObj;\n  let s;\n  return o.parent ? s = (i = n.closest(\"me-main\")) == null ? void 0 : i.className : s = (r = C(o.children[t].id).closest(\"me-main\")) == null ? void 0 : r.className, s;\n}, et = function(e, t) {\n  var _;\n  const { id: n, text: o, parent: s, start: i, end: r } = t, c = e.nodes, h = C(s).nodeObj, a = Kn(t);\n  let d = 1 / 0, u = 0, g = 0, p = 0;\n  for (let P = i; P <= r; P++) {\n    const U = (_ = h.children) == null ? void 0 : _[P];\n    if (!U)\n      return e.removeSummary(n), null;\n    const H = In(U.id), { offsetLeft: Z, offsetTop: be } = $(c, H);\n    P === i && (g = be), P === r && (p = be + H.offsetHeight), Z < d && (d = Z), H.offsetWidth + Z > u && (u = H.offsetWidth + Z);\n  }\n  let m, f;\n  const v = g + 10, b = p + 10, y = (v + b) / 2, x = e.theme.cssVar[\"--color\"];\n  a === \"lhs\" ? (m = je(`M ${d + 10} ${v} c -5 0 -10 5 -10 10 L ${d} ${b - 10} c 0 5 5 10 10 10 M ${d} ${y} h -10`, x), f = De(o, d - 20, y + 6, \"end\", x)) : (m = je(`M ${u - 10} ${v} c 5 0 10 5 10 10 L ${u} ${b - 10} c 0 5 -5 10 -10 10 M ${u} ${y} h 10`, x), f = De(o, u + 20, y + 6, \"start\", x));\n  const w = Fn(\"s-\" + n);\n  return w.appendChild(m), w.appendChild(f), w.summaryObj = t, e.summarySvg.appendChild(w), w;\n}, Vn = function() {\n  let e = [];\n  this.currentNode ? e = [this.currentNode] : this.currentNodes && (e = this.currentNodes);\n  const { parent: t, start: n, end: o } = qn(e), s = { id: G(), parent: t, start: n, end: o, text: \"summary\" }, i = et(this, s);\n  this.summaries.push(s), this.editSummary(i), this.bus.fire(\"operation\", {\n    name: \"createSummary\",\n    obj: s\n  });\n}, Wn = function(e) {\n  var n;\n  const t = this.summaries.findIndex((o) => o.id === e);\n  t > -1 && (this.summaries.splice(t, 1), (n = document.querySelector(\"#s-\" + e)) == null || n.remove()), this.bus.fire(\"operation\", {\n    name: \"removeSummary\",\n    obj: { id: e }\n  });\n}, Un = function(e) {\n  const t = e.children[1].getBBox(), n = 6, o = 3, s = document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n  N(s, {\n    x: t.x - n + \"\",\n    y: t.y - n + \"\",\n    width: t.width + n * 2 + \"\",\n    height: t.height + n * 2 + \"\",\n    rx: o + \"\",\n    stroke: this.theme.cssVar[\"--selected\"] || \"#4dc4ff\",\n    \"stroke-width\": \"2\",\n    fill: \"none\"\n  }), s.classList.add(\"selected\"), e.appendChild(s), this.currentSummary = e;\n}, Yn = function() {\n  var e, t;\n  (t = (e = this.currentSummary) == null ? void 0 : e.querySelector(\"rect\")) == null || t.remove(), this.currentSummary = null;\n}, Xn = function() {\n  this.summarySvg.innerHTML = \"\", this.summaries.forEach((e) => {\n    try {\n      et(this, e);\n    } catch {\n    }\n  }), this.nodes.insertAdjacentElement(\"beforeend\", this.summarySvg);\n}, Gn = function(e) {\n  if (!e)\n    return;\n  const t = e.childNodes[1];\n  Re(this, t, (n) => {\n    var i;\n    const o = e.summaryObj, s = ((i = n.textContent) == null ? void 0 : i.trim()) || \"\";\n    s === \"\" ? o.text = origin : o.text = s, n.remove(), s !== origin && (t.innerHTML = o.text, this.linkDiv(), this.bus.fire(\"operation\", {\n      name: \"finishEditSummary\",\n      obj: o\n    }));\n  });\n}, Jn = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  createSummary: Vn,\n  editSummary: Gn,\n  removeSummary: Wn,\n  renderSummary: Xn,\n  selectSummary: Un,\n  unselectSummary: Yn\n}, Symbol.toStringTag, { value: \"Module\" })), M = \"http://www.w3.org/2000/svg\";\nfunction Zn(e, t) {\n  const n = document.createElementNS(M, \"svg\");\n  return N(n, {\n    version: \"1.1\",\n    xmlns: M,\n    height: e,\n    width: t\n  }), n;\n}\nfunction Qn(e, t) {\n  return (parseInt(e) - parseInt(t)) / 2;\n}\nfunction eo(e, t, n, o) {\n  const s = document.createElementNS(M, \"g\");\n  let i = \"\";\n  return e.text ? i = e.text.textContent : i = e.childNodes[0].textContent, i.split(`\n`).forEach((c, l) => {\n    const h = document.createElementNS(M, \"text\");\n    N(h, {\n      x: n + parseInt(t.paddingLeft) + \"\",\n      y: o + parseInt(t.paddingTop) + Qn(t.lineHeight, t.fontSize) * (l + 1) + parseFloat(t.fontSize) * (l + 1) + \"\",\n      \"text-anchor\": \"start\",\n      \"font-family\": t.fontFamily,\n      \"font-size\": `${t.fontSize}`,\n      \"font-weight\": `${t.fontWeight}`,\n      fill: `${t.color}`\n    }), h.innerHTML = c, s.appendChild(h);\n  }), s;\n}\nfunction to(e, t, n, o) {\n  var c;\n  let s = \"\";\n  (c = e.nodeObj) != null && c.dangerouslySetInnerHTML ? s = e.nodeObj.dangerouslySetInnerHTML : e.text ? s = e.text.textContent : s = e.childNodes[0].textContent;\n  const i = document.createElementNS(M, \"foreignObject\");\n  N(i, {\n    x: n + parseInt(t.paddingLeft) + \"\",\n    y: o + parseInt(t.paddingTop) + \"\",\n    width: t.width,\n    height: t.height\n  });\n  const r = document.createElement(\"div\");\n  return N(r, {\n    xmlns: \"http://www.w3.org/1999/xhtml\",\n    style: `font-family: ${t.fontFamily}; font-size: ${t.fontSize}; font-weight: ${t.fontWeight}; color: ${t.color}; white-space: pre-wrap;`\n  }), r.innerHTML = s, i.appendChild(r), i;\n}\nfunction no(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = $(e.nodes, t), i = document.createElementNS(M, \"rect\");\n  return N(i, {\n    x: o + \"\",\n    y: s + \"\",\n    rx: n.borderRadius,\n    ry: n.borderRadius,\n    width: n.width,\n    height: n.height,\n    fill: n.backgroundColor,\n    stroke: n.borderColor,\n    \"stroke-width\": n.borderWidth\n  }), i;\n}\nfunction te(e, t, n = !1) {\n  const o = getComputedStyle(t), { offsetLeft: s, offsetTop: i } = $(e.nodes, t), r = document.createElementNS(M, \"rect\");\n  N(r, {\n    x: s + \"\",\n    y: i + \"\",\n    rx: o.borderRadius,\n    ry: o.borderRadius,\n    width: o.width,\n    height: o.height,\n    fill: o.backgroundColor,\n    stroke: o.borderColor,\n    \"stroke-width\": o.borderWidth\n  });\n  const c = document.createElementNS(M, \"g\");\n  c.appendChild(r);\n  let l;\n  return n ? l = to(t, o, s, i) : l = eo(t, o, s, i), c.appendChild(l), c;\n}\nfunction oo(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = $(e.nodes, t), i = document.createElementNS(M, \"a\"), r = document.createElementNS(M, \"text\");\n  return N(r, {\n    x: o + \"\",\n    y: s + parseInt(n.fontSize) + \"\",\n    \"text-anchor\": \"start\",\n    \"font-family\": n.fontFamily,\n    \"font-size\": `${n.fontSize}`,\n    \"font-weight\": `${n.fontWeight}`,\n    fill: `${n.color}`\n  }), r.innerHTML = t.textContent, i.appendChild(r), i.setAttribute(\"href\", t.href), i;\n}\nfunction so(e, t) {\n  const n = getComputedStyle(t), { offsetLeft: o, offsetTop: s } = $(e.nodes, t), i = document.createElementNS(M, \"image\");\n  return N(i, {\n    x: o + \"\",\n    y: s + \"\",\n    width: n.width + \"\",\n    height: n.height + \"\",\n    href: t.src\n  }), i;\n}\nconst ne = 100, io = '<?xml version=\"1.0\" standalone=\"no\"?><!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">', ro = (e, t = !1) => {\n  var d, u, g;\n  const n = e.nodes, o = n.offsetHeight + ne * 2, s = n.offsetWidth + ne * 2, i = Zn(o + \"px\", s + \"px\"), r = document.createElementNS(M, \"svg\"), c = document.createElementNS(M, \"rect\");\n  N(c, {\n    x: \"0\",\n    y: \"0\",\n    width: `${s}`,\n    height: `${o}`,\n    fill: e.theme.cssVar[\"--bgcolor\"]\n  }), i.appendChild(c), n.querySelectorAll(\".subLines\").forEach((p) => {\n    const m = p.cloneNode(!0), { offsetLeft: f, offsetTop: v } = $(n, p.parentElement);\n    m.setAttribute(\"x\", `${f}`), m.setAttribute(\"y\", `${v}`), r.appendChild(m);\n  });\n  const l = (d = n.querySelector(\".lines\")) == null ? void 0 : d.cloneNode(!0);\n  l && r.appendChild(l);\n  const h = (u = n.querySelector(\".topiclinks\")) == null ? void 0 : u.cloneNode(!0);\n  h && r.appendChild(h);\n  const a = (g = n.querySelector(\".summary\")) == null ? void 0 : g.cloneNode(!0);\n  return a && r.appendChild(a), n.querySelectorAll(\"me-tpc\").forEach((p) => {\n    p.nodeObj.dangerouslySetInnerHTML ? r.appendChild(te(e, p, !t)) : (r.appendChild(no(e, p)), r.appendChild(te(e, p.text, !t)));\n  }), n.querySelectorAll(\".tags > span\").forEach((p) => {\n    r.appendChild(te(e, p));\n  }), n.querySelectorAll(\".icons > span\").forEach((p) => {\n    r.appendChild(te(e, p));\n  }), n.querySelectorAll(\".hyper-link\").forEach((p) => {\n    r.appendChild(oo(e, p));\n  }), n.querySelectorAll(\"img\").forEach((p) => {\n    r.appendChild(so(e, p));\n  }), N(r, {\n    x: ne + \"\",\n    y: ne + \"\",\n    overflow: \"visible\"\n  }), i.appendChild(r), i;\n}, co = (e, t) => (t && e.insertAdjacentHTML(\"afterbegin\", \"<style>\" + t + \"</style>\"), io + e.outerHTML);\nfunction lo(e) {\n  return new Promise((t, n) => {\n    const o = new FileReader();\n    o.onload = (s) => {\n      t(s.target.result);\n    }, o.onerror = (s) => {\n      n(s);\n    }, o.readAsDataURL(e);\n  });\n}\nconst ao = function(e = !1, t) {\n  const n = ro(this, e), o = co(n, t);\n  return new Blob([o], { type: \"image/svg+xml\" });\n}, ho = async function(e = !1, t) {\n  const n = this.exportSvg(e, t), o = await lo(n);\n  return new Promise((s, i) => {\n    const r = new Image();\n    r.setAttribute(\"crossOrigin\", \"anonymous\"), r.onload = () => {\n      const c = document.createElement(\"canvas\");\n      c.width = r.width, c.height = r.height, c.getContext(\"2d\").drawImage(r, 0, 0), c.toBlob(s, \"image/png\", 1);\n    }, r.src = o, r.onerror = i;\n  });\n}, uo = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  exportPng: ho,\n  exportSvg: ao\n}, Symbol.toStringTag, { value: \"Module\" }));\nfunction fo(e, t) {\n  return async function(...n) {\n    const o = this.before[t];\n    o && !await o.apply(this, n) || e.apply(this, n);\n  };\n}\nconst Oe = Object.keys(Xe), tt = {};\nfor (let e = 0; e < Oe.length; e++) {\n  const t = Oe[e];\n  tt[t] = fo(Xe[t], t);\n}\nconst po = {\n  getObjById: se,\n  generateNewObj: st,\n  layout: ct,\n  linkDiv: gt,\n  editTopic: pt,\n  createWrapper: dt,\n  createParent: ht,\n  createChildren: ut,\n  createTopic: ft,\n  findEle: C,\n  changeTheme: qt,\n  ...Tn,\n  ...tt,\n  ...Rn,\n  ...Jn,\n  ...uo,\n  init(e) {\n    if (!e || !e.nodeData)\n      return new Error(\"MindElixir: `data` is required\");\n    e.direction !== void 0 && (this.direction = e.direction), this.changeTheme(e.theme || this.theme, !1), this.nodeData = e.nodeData, O(this.nodeData), this.arrows = e.arrows || [], this.summaries = e.summaries || [], this.tidyArrow(), this.toolBar && jt(this), this.keypress && Nt(this), this.editable && Rt(this), nt() && this.mobileMenu ? Ct(this) : this.contextMenu && this.disposable.push(vt(this, this.contextMenuOption)), this.draggable && Mt(this), this.allowUndo && Lt(this), this.toCenter(), this.layout(), this.linkDiv();\n  },\n  destroy() {\n    var e, t;\n    this.disposable.forEach((n) => n()), (e = this.mindElixirBox) == null || e.remove(), this.mindElixirBox = void 0, this.nodeData = void 0, this.arrows = void 0, this.summaries = void 0, this.currentArrow = void 0, this.currentNode = void 0, this.currentNodes = void 0, this.currentSummary = void 0, this.waitCopy = void 0, this.theme = void 0, this.direction = void 0, this.bus = void 0, this.container = void 0, this.map = void 0, this.lines = void 0, this.linkController = void 0, this.linkSvgGroup = void 0, this.P2 = void 0, this.P3 = void 0, this.line1 = void 0, this.line2 = void 0, this.nodes = void 0, (t = this.selection) == null || t.destroy(), this.selection = void 0;\n  }\n};\nfunction mo({ pT: e, pL: t, pW: n, pH: o, cT: s, cL: i, cW: r, cH: c, direction: l, containerHeight: h }) {\n  let a = t + n / 2;\n  const d = e + o / 2;\n  let u;\n  l === \"lhs\" ? u = i + r : u = i;\n  const g = s + c / 2, m = (1 - Math.abs(g - d) / h) * 0.25 * (n / 2);\n  return l === \"lhs\" ? a = a - n / 10 - m : a = a + n / 10 + m, `M ${a} ${d} Q ${a} ${g} ${u} ${g}`;\n}\nfunction go({ pT: e, pL: t, pW: n, pH: o, cT: s, cL: i, cW: r, cH: c, direction: l, isFirst: h }) {\n  let a = 0, d = 0;\n  h ? a = e + o / 2 : a = e + o;\n  const u = s + c;\n  let g = 0, p = 0, m = 0;\n  const f = Math.abs(a - u) / 300 * D;\n  return l === \"lhs\" ? (m = t, g = m + D, p = m - D, d = i + D, `M ${g} ${a} C ${m} ${a} ${m + f} ${u} ${p} ${u} H ${d}`) : (m = t + n, g = m - D, p = m + D, d = i + r - D, `M ${g} ${a} C ${m} ${a} ${m - f} ${u} ${p} ${u} H ${d}`);\n}\nconst vo = \"4.1.5\", Y = document;\nfunction A({\n  el: e,\n  direction: t,\n  locale: n,\n  draggable: o,\n  editable: s,\n  contextMenu: i,\n  contextMenuOption: r,\n  toolBar: c,\n  keypress: l,\n  mouseSelectionButton: h,\n  before: a,\n  newTopicName: d,\n  allowUndo: u,\n  generateMainBranch: g,\n  generateSubBranch: p,\n  overflowHidden: m,\n  mobileMenu: f,\n  theme: v\n}) {\n  let b = null;\n  const y = Object.prototype.toString.call(e);\n  if (y === \"[object HTMLDivElement]\" ? b = e : y === \"[object String]\" && (b = document.querySelector(e)), !b)\n    throw new Error(\"MindElixir: el is not a valid element\");\n  b.className += \" mind-elixir\", b.innerHTML = \"\", b.style.setProperty(\"--gap\", D + \"px\"), this.mindElixirBox = b, this.disposable = [], this.before = a || {}, this.locale = n || \"en\", this.contextMenuOption = r, this.contextMenu = i === void 0 ? !0 : i, this.toolBar = c === void 0 ? !0 : c, this.keypress = l === void 0 ? !0 : l, this.mouseSelectionButton = h || 0, this.mobileMenu = f || !1, this.direction = typeof t == \"number\" ? t : 1, this.draggable = o === void 0 ? !0 : o, this.newTopicName = d || \"new node\", this.editable = s === void 0 ? !0 : s, this.allowUndo = u === void 0 ? !1 : u, this.currentNode = null, this.currentArrow = null, this.scaleVal = 1, this.tempDirection = null, this.generateMainBranch = g || mo, this.generateSubBranch = p || go, this.overflowHidden = m || !1, this.bus = rt.create(), this.container = Y.createElement(\"div\"), this.container.className = \"map-container\";\n  const x = window.matchMedia(\"(prefers-color-scheme: dark)\");\n  this.theme = v || (x.matches ? Pe : $e);\n  const w = Y.createElement(\"div\");\n  w.className = \"map-canvas\", this.map = w, this.map.setAttribute(\"tabindex\", \"0\"), this.container.appendChild(this.map), this.mindElixirBox.appendChild(this.container), this.nodes = Y.createElement(\"me-nodes\"), this.nodes.className = \"main-node-container\", this.lines = X(\"lines\"), this.summarySvg = X(\"summary\"), this.linkController = X(\"linkcontroller\"), this.P2 = Y.createElement(\"div\"), this.P3 = Y.createElement(\"div\"), this.P2.className = this.P3.className = \"circle\", this.P2.style.display = this.P3.style.display = \"none\", this.line1 = ye(), this.line2 = ye(), this.linkController.appendChild(this.line1), this.linkController.appendChild(this.line2), this.linkSvgGroup = X(\"topiclinks\"), this.map.appendChild(this.nodes), this.overflowHidden ? this.container.style.overflow = \"hidden\" : it(this);\n}\nA.prototype = po;\nA.LEFT = L;\nA.RIGHT = R;\nA.SIDE = ce;\nA.THEME = $e;\nA.DARK_THEME = Pe;\nA.version = vo;\nA.E = C;\nA.new = (e) => ({\n  nodeData: {\n    id: G(),\n    topic: e || \"new topic\",\n    children: []\n  }\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/MindElixir.js\n"));

/***/ })

}]);