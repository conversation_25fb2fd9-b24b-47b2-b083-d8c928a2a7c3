'use client'

import { useState } from 'react'
import MindElixirReact from './MindElixirReact'
import { MindMapItem } from '@/models/list'
import { Options } from 'mind-elixir'

interface MindMapCardProps {
  map: MindMapItem
  type: 'public' | 'private'
  className?: string
  onDelete: () => void
  onDownload: (type: string) => void
  onMakePublic: () => void
  onShare: () => void
}

export default function MindMapCard({
  map,
  type,
  className,
  onDelete,
  onDownload,
  onMakePublic,
  onShare,
}: MindMapCardProps) {
  const [showDropdown, setShowDropdown] = useState(false)

  const options: Options = {
    el: '',
    direction: 2,
    draggable: false,
    editable: false,
    contextMenu: false,
    toolBar: false,
    keypress: false,
  }

  const timeFormatter = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className={`card box-content bg-base-100 shadow ${className}`}>
      <figure className="w-full aspect-video">
        <MindElixirReact
          data={map.content}
          options={options}
          initScale={0.2}
          className="h-full w-full"
        />
      </figure>
      <div className="m-4">
        <div className="flex items-center">
          <h2 className="font-bold overflow-hidden whitespace-nowrap text-ellipsis">
            {map.name}
          </h2>
          {map.public && <span className="badge ml-1">Public</span>}
        </div>
        <p>{timeFormatter(map.updatedAt || map.date)}</p>
      </div>
      
      {type === 'private' && (
        <div className="dropdown dropdown-end absolute right-2 top-2">
          <label
            tabIndex={0}
            className="btn btn-ghost btn-circle btn-sm"
            onClick={(e) => {
              e.preventDefault()
              setShowDropdown(!showDropdown)
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </label>
          {showDropdown && (
            <ul
              tabIndex={0}
              className="dropdown-content z-[1] menu p-2 shadow rounded-box w-52 bg-base-100"
              onClick={(e) => e.preventDefault()}
            >
              <li onClick={onMakePublic}>
                <a>{map.public ? 'Make Private' : 'Make Public'}</a>
              </li>
              <li onClick={onShare}>
                <a>Share</a>
              </li>
              <li>
                <details>
                  <summary>Download</summary>
                  <ul className="p-2">
                    <li onClick={() => onDownload('json')}>
                      <a>JSON</a>
                    </li>
                    <li onClick={() => onDownload('html')}>
                      <a>HTML</a>
                    </li>
                    <li onClick={() => onDownload('xmind')}>
                      <a>XMind</a>
                    </li>
                  </ul>
                </details>
              </li>
              <li onClick={onDelete}>
                <a className="text-error">Delete</a>
              </li>
            </ul>
          )}
        </div>
      )}
    </div>
  )
}
