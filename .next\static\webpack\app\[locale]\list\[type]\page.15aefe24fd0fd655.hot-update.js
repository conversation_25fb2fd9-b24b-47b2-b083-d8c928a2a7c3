"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/list/[type]/page",{

/***/ "(app-pages-browser)/./src/connect.ts":
/*!************************!*\
  !*** ./src/connect.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/.pnpm/axios@1.7.7/node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/toast */ \"(app-pages-browser)/./src/utils/toast.ts\");\n\n\nconst relink = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"http://localhost:8080\" || 0,\n    withCredentials: true\n});\nrelink.interceptors.response.use(function(res) {\n    return res.data;\n}, function(error) {\n    if (error.response.status !== 401) {\n        _utils_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"Network error\");\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (relink);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25uZWN0LnRzIiwibWFwcGluZ3MiOiI7OztBQUF5QjtBQUNRO0FBRWpDLE1BQU1FLFNBQVNGLDZDQUFLQSxDQUFDRyxNQUFNLENBQUM7SUFDMUJDLFNBQVNDLHVCQUFtQyxJQUFJO0lBQ2hERyxpQkFBaUI7QUFDbkI7QUFFQU4sT0FBT08sWUFBWSxDQUFDQyxRQUFRLENBQUNDLEdBQUcsQ0FDOUIsU0FBVUMsR0FBRztJQUNYLE9BQU9BLElBQUlDLElBQUk7QUFDakIsR0FDQSxTQUFVQyxLQUFLO0lBQ2IsSUFBSUEsTUFBTUosUUFBUSxDQUFDSyxNQUFNLEtBQUssS0FBSztRQUNqQ2Qsb0RBQUtBLENBQUNhLEtBQUssQ0FBQztJQUNkO0lBQ0EsT0FBT0UsUUFBUUMsTUFBTSxDQUFDSDtBQUN4QjtBQUdGLCtEQUFlWixNQUFNQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb25uZWN0LnRzPzMyMmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJ1xyXG5pbXBvcnQgdG9hc3QgZnJvbSAnLi91dGlscy90b2FzdCdcclxuXHJcbmNvbnN0IHJlbGluayA9IGF4aW9zLmNyZWF0ZSh7XHJcbiAgYmFzZVVSTDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VVUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6NzAwMScsXHJcbiAgd2l0aENyZWRlbnRpYWxzOiB0cnVlLFxyXG59KVxyXG5cclxucmVsaW5rLmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoXHJcbiAgZnVuY3Rpb24gKHJlcykge1xyXG4gICAgcmV0dXJuIHJlcy5kYXRhXHJcbiAgfSxcclxuICBmdW5jdGlvbiAoZXJyb3IpIHtcclxuICAgIGlmIChlcnJvci5yZXNwb25zZS5zdGF0dXMgIT09IDQwMSkge1xyXG4gICAgICB0b2FzdC5lcnJvcignTmV0d29yayBlcnJvcicpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXHJcbiAgfVxyXG4pXHJcblxyXG5leHBvcnQgZGVmYXVsdCByZWxpbmtcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwidG9hc3QiLCJyZWxpbmsiLCJjcmVhdGUiLCJiYXNlVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFVVJMIiwid2l0aENyZWRlbnRpYWxzIiwiaW50ZXJjZXB0b3JzIiwicmVzcG9uc2UiLCJ1c2UiLCJyZXMiLCJkYXRhIiwiZXJyb3IiLCJzdGF0dXMiLCJQcm9taXNlIiwicmVqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/connect.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/providers/UserProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/UserProvider.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: function() { return /* binding */ UserProvider; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/connect */ \"(app-pages-browser)/./src/connect.ts\");\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider(param) {\n    let { children } = param;\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const refreshUser = async ()=>{\n        try {\n            const res = await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/user\");\n            if (res.data && res.data.providerAccountId) {\n                setUserData(res.data);\n            } else {\n                setUserData(undefined);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user:\", error);\n            setUserData(undefined);\n        }\n    };\n    const login = async (provider)=>{\n        setLoading(true);\n        try {\n            if (provider === \"guest\") {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/auth/guest\");\n                if (res.data) {\n                    setUserData(res.data);\n                }\n            } else {\n                // 重定向到OAuth提供商\n                window.location.href = \"\".concat(\"http://localhost:8080\", \"/api/auth/\").concat(provider);\n            }\n        } catch (error) {\n            console.error(\"\".concat(provider, \" login failed:\"), error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/auth/logout\");\n            setUserData(undefined);\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n            // 即使API调用失败，也清除本地状态\n            setUserData(undefined);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUser = async ()=>{\n            setLoading(true);\n            await refreshUser();\n            setLoading(false);\n        };\n        fetchUser();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            userData,\n            setUserData,\n            loading,\n            login,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\providers\\\\UserProvider.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(UserProvider, \"CNTVQNiY3jZ9qK7jTzFiwu5lcTg=\");\n_c = UserProvider;\nfunction useUser() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n_s1(useUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/providers/UserProvider.tsx\n"));

/***/ })

});