/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./cn.json": [
		"(rsc)/./messages/cn.json",
		"_rsc_messages_cn_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1d1c\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/NavBar.tsx */ \"(ssr)/./src/components/NavBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/UserProvider.tsx */ \"(ssr)/./src/providers/UserProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.26.5_next%4014.2.29_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Ccomponents%5C%5CNavBar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Cproviders%5C%5CUserProvider.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(ssr)/./src/app/[locale]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjI5X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDZ2l0JTVDJTVDbWluZC1lbGl4aXItY2xvdWQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTZGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvP2I4MGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxnaXRcXFxcbWluZC1lbGl4aXItY2xvdWRcXFxcc3JjXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cgit%5C%5Cmind-elixir-cloud%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mind-elixir/example */ \"(ssr)/./node_modules/.pnpm/mind-elixir@4.1.5_@types+node@20.16.11/node_modules/mind-elixir/dist/example.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MindElixirReact = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\[locale]\\\\page.tsx -> \" + \"@/components/MindElixirReact\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 18\n        }, undefined)\n});\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MindElixirReact, {\n            data: mind_elixir_example__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1tsb2NhbGVdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrQztBQUNJO0FBRXRDLE1BQU1FLGtCQUFrQkYsd0RBQU9BOzs7Ozs7OztJQUM3QkcsS0FBSztJQUNMQyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVO3NCQUE0Qzs7Ozs7OztBQUc3RCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0Y7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0o7WUFBZ0JELE1BQU1BLDJEQUFJQTs7Ozs7Ozs7Ozs7QUFHakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8uL3NyYy9hcHAvW2xvY2FsZV0vcGFnZS50c3g/YTQ3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJ1xuaW1wb3J0IGRhdGEgZnJvbSAnbWluZC1lbGl4aXIvZXhhbXBsZSdcblxuY29uc3QgTWluZEVsaXhpclJlYWN0ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9NaW5kRWxpeGlyUmVhY3QnKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+TG9hZGluZy4uLjwvZGl2PlxufSlcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZVBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlblwiPlxuICAgICAgPE1pbmRFbGl4aXJSZWFjdCBkYXRhPXtkYXRhfSAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiZHluYW1pYyIsImRhdGEiLCJNaW5kRWxpeGlyUmVhY3QiLCJzc3IiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiSG9tZVBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NavBar.tsx":
/*!***********************************!*\
  !*** ./src/components/NavBar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NavBar({ className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const availableLocales = [\n        \"en\",\n        \"cn\",\n        \"ja\"\n    ];\n    const changeLocale = (newLocale)=>{\n        const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);\n        router.push(newPath);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `navbar bg-base-100 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                tabIndex: 0,\n                                className: \"btn btn-ghost lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M4 6h16M4 12h8m-8 6h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                tabIndex: 0,\n                                className: \"menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow rounded-box w-52\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/list/public\",\n                                            children: t(\"menu.public\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/list/map\",\n                                            children: t(\"menu.folder\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/about\",\n                                            children: t(\"menu.about\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        tabIndex: 0,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    children: \"i18n\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"p-2\",\n                                                    children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            onClick: ()=>changeLocale(localeOption),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                children: localeOption\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, localeOption, false, {\n                                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"btn btn-ghost normal-case text-xl !pl-0\",\n                        href: \"/\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: \"/logo2.png\",\n                                alt: \"Logo\",\n                                width: 32,\n                                height: 32,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            \"Mind Elixir\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-center hidden lg:flex\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"menu menu-horizontal px-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/list/public\",\n                                children: t(\"menu.public\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/list/map\",\n                                children: t(\"menu.folder\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/about\",\n                                children: t(\"menu.about\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            tabIndex: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        children: \"i18n\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"p-2\",\n                                        children: availableLocales.map((localeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                onClick: ()=>changeLocale(localeOption),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    children: localeOption\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, localeOption, false, {\n                                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-end\"\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\components\\\\NavBar.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OYXZCYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVzRDtBQUNFO0FBQzVCO0FBQ0U7QUFNZixTQUFTTSxPQUFPLEVBQUVDLFNBQVMsRUFBZTtJQUN2RCxNQUFNQyxJQUFJUiwwREFBZUE7SUFDekIsTUFBTVMsU0FBU1Isb0RBQVNBO0lBQ3hCLE1BQU1TLFNBQVNSLDBEQUFTQTtJQUN4QixNQUFNUyxXQUFXUiw0REFBV0E7SUFFNUIsTUFBTVMsbUJBQW1CO1FBQUM7UUFBTTtRQUFNO0tBQUs7SUFFM0MsTUFBTUMsZUFBZSxDQUFDQztRQUNwQixNQUFNQyxVQUFVSixTQUFTSyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUVQLE9BQU8sQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFSyxVQUFVLENBQUM7UUFDOURKLE9BQU9PLElBQUksQ0FBQ0Y7SUFDZDtJQUVBLHFCQUNFLDhEQUFDRztRQUFJWCxXQUFXLENBQUMsbUJBQW1CLEVBQUVBLFVBQVUsQ0FBQzs7MEJBQy9DLDhEQUFDVztnQkFBSVgsV0FBVTs7a0NBQ2IsOERBQUNXO3dCQUFJWCxXQUFVOzswQ0FDYiw4REFBQ1k7Z0NBQU1DLFVBQVU7Z0NBQUdiLFdBQVU7MENBQzVCLDRFQUFDYztvQ0FDQ0MsT0FBTTtvQ0FDTmYsV0FBVTtvQ0FDVmdCLE1BQUs7b0NBQ0xDLFNBQVE7b0NBQ1JDLFFBQU87OENBRVAsNEVBQUNDO3dDQUNDQyxlQUFjO3dDQUNkQyxnQkFBZTt3Q0FDZkMsYUFBWTt3Q0FDWkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJUiw4REFBQ0M7Z0NBQ0NYLFVBQVU7Z0NBQ1ZiLFdBQVU7O2tEQUVWLDhEQUFDeUI7a0RBQ0MsNEVBQUM1QixpREFBSUE7NENBQUM2QixNQUFLO3NEQUFnQnpCLEVBQUU7Ozs7Ozs7Ozs7O2tEQUUvQiw4REFBQ3dCO2tEQUNDLDRFQUFDNUIsaURBQUlBOzRDQUFDNkIsTUFBSztzREFBYXpCLEVBQUU7Ozs7Ozs7Ozs7O2tEQUU1Qiw4REFBQ3dCO2tEQUNDLDRFQUFDNUIsaURBQUlBOzRDQUFDNkIsTUFBSztzREFBVXpCLEVBQUU7Ozs7Ozs7Ozs7O2tEQUV6Qiw4REFBQ3dCO3dDQUFHWixVQUFVO2tEQUNaLDRFQUFDYzs7OERBQ0MsOERBQUNDOzhEQUFROzs7Ozs7OERBQ1QsOERBQUNKO29EQUFHeEIsV0FBVTs4REFDWEssaUJBQWlCd0IsR0FBRyxDQUFDLENBQUNDLDZCQUNyQiw4REFBQ0w7NERBQXNCTSxTQUFTLElBQU16QixhQUFhd0I7c0VBQ2pELDRFQUFDRTswRUFBR0Y7Ozs7OzsyREFER0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTckIsOERBQUNqQyxpREFBSUE7d0JBQUNHLFdBQVU7d0JBQTBDMEIsTUFBSzs7MENBQzdELDhEQUFDNUIsa0RBQUtBO2dDQUFDbUMsS0FBSTtnQ0FBYUMsS0FBSTtnQ0FBT0MsT0FBTztnQ0FBSUMsUUFBUTtnQ0FBSXBDLFdBQVU7Ozs7Ozs0QkFBVzs7Ozs7Ozs7Ozs7OzswQkFJbkYsOERBQUNXO2dCQUFJWCxXQUFVOzBCQUNiLDRFQUFDd0I7b0JBQUd4QixXQUFVOztzQ0FDWiw4REFBQ3lCO3NDQUNDLDRFQUFDNUIsaURBQUlBO2dDQUFDNkIsTUFBSzswQ0FBZ0J6QixFQUFFOzs7Ozs7Ozs7OztzQ0FFL0IsOERBQUN3QjtzQ0FDQyw0RUFBQzVCLGlEQUFJQTtnQ0FBQzZCLE1BQUs7MENBQWF6QixFQUFFOzs7Ozs7Ozs7OztzQ0FFNUIsOERBQUN3QjtzQ0FDQyw0RUFBQzVCLGlEQUFJQTtnQ0FBQzZCLE1BQUs7MENBQVV6QixFQUFFOzs7Ozs7Ozs7OztzQ0FFekIsOERBQUN3Qjs0QkFBR1osVUFBVTtzQ0FDWiw0RUFBQ2M7O2tEQUNDLDhEQUFDQztrREFBUTs7Ozs7O2tEQUNULDhEQUFDSjt3Q0FBR3hCLFdBQVU7a0RBQ1hLLGlCQUFpQndCLEdBQUcsQ0FBQyxDQUFDQyw2QkFDckIsOERBQUNMO2dEQUFzQk0sU0FBUyxJQUFNekIsYUFBYXdCOzBEQUNqRCw0RUFBQ0U7OERBQUdGOzs7Ozs7K0NBREdBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTckIsOERBQUNuQjtnQkFBSVgsV0FBVTs7Ozs7Ozs7Ozs7O0FBS3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvY29tcG9uZW50cy9OYXZCYXIudHN4P2Q5ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucywgdXNlTG9jYWxlIH0gZnJvbSAnbmV4dC1pbnRsJ1xuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuXG5pbnRlcmZhY2UgTmF2QmFyUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmF2QmFyKHsgY2xhc3NOYW1lIH06IE5hdkJhclByb3BzKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoKVxuICBjb25zdCBsb2NhbGUgPSB1c2VMb2NhbGUoKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKClcblxuICBjb25zdCBhdmFpbGFibGVMb2NhbGVzID0gWydlbicsICdjbicsICdqYSddXG5cbiAgY29uc3QgY2hhbmdlTG9jYWxlID0gKG5ld0xvY2FsZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbmV3UGF0aCA9IHBhdGhuYW1lLnJlcGxhY2UoYC8ke2xvY2FsZX1gLCBgLyR7bmV3TG9jYWxlfWApXG4gICAgcm91dGVyLnB1c2gobmV3UGF0aClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BuYXZiYXIgYmctYmFzZS0xMDAgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm5hdmJhci1zdGFydFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duXCI+XG4gICAgICAgICAgPGxhYmVsIHRhYkluZGV4PXswfSBjbGFzc05hbWU9XCJidG4gYnRuLWdob3N0IGxnOmhpZGRlblwiPlxuICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC01IHctNVwiXG4gICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxuICAgICAgICAgICAgICAgIGQ9XCJNNCA2aDE2TTQgMTJoOG0tOCA2aDE2XCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPHVsXG4gICAgICAgICAgICB0YWJJbmRleD17MH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1lbnUgbWVudS1zbSBkcm9wZG93bi1jb250ZW50IG10LTMgei1bMV0gcC0yIHNoYWRvdyByb3VuZGVkLWJveCB3LTUyXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbGlzdC9wdWJsaWNcIj57dCgnbWVudS5wdWJsaWMnKX08L0xpbms+XG4gICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2xpc3QvbWFwXCI+e3QoJ21lbnUuZm9sZGVyJyl9PC9MaW5rPlxuICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiPnt0KCdtZW51LmFib3V0Jyl9PC9MaW5rPlxuICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDxsaSB0YWJJbmRleD17MH0+XG4gICAgICAgICAgICAgIDxkZXRhaWxzPlxuICAgICAgICAgICAgICAgIDxzdW1tYXJ5PmkxOG48L3N1bW1hcnk+XG4gICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInAtMlwiPlxuICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZUxvY2FsZXMubWFwKChsb2NhbGVPcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17bG9jYWxlT3B0aW9ufSBvbkNsaWNrPXsoKSA9PiBjaGFuZ2VMb2NhbGUobG9jYWxlT3B0aW9uKX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGE+e2xvY2FsZU9wdGlvbn08L2E+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICA8L2RldGFpbHM+XG4gICAgICAgICAgICA8L2xpPlxuICAgICAgICAgIDwvdWw+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8TGluayBjbGFzc05hbWU9XCJidG4gYnRuLWdob3N0IG5vcm1hbC1jYXNlIHRleHQteGwgIXBsLTBcIiBocmVmPVwiL1wiPlxuICAgICAgICAgIDxJbWFnZSBzcmM9XCIvbG9nbzIucG5nXCIgYWx0PVwiTG9nb1wiIHdpZHRoPXszMn0gaGVpZ2h0PXszMn0gY2xhc3NOYW1lPVwiaC1mdWxsXCIgLz5cbiAgICAgICAgICBNaW5kIEVsaXhpclxuICAgICAgICA8L0xpbms+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibmF2YmFyLWNlbnRlciBoaWRkZW4gbGc6ZmxleFwiPlxuICAgICAgICA8dWwgY2xhc3NOYW1lPVwibWVudSBtZW51LWhvcml6b250YWwgcHgtMVwiPlxuICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbGlzdC9wdWJsaWNcIj57dCgnbWVudS5wdWJsaWMnKX08L0xpbms+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgICA8bGk+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL2xpc3QvbWFwXCI+e3QoJ21lbnUuZm9sZGVyJyl9PC9MaW5rPlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgPGxpPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiPnt0KCdtZW51LmFib3V0Jyl9PC9MaW5rPlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgPGxpIHRhYkluZGV4PXswfT5cbiAgICAgICAgICAgIDxkZXRhaWxzPlxuICAgICAgICAgICAgICA8c3VtbWFyeT5pMThuPC9zdW1tYXJ5PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwicC0yXCI+XG4gICAgICAgICAgICAgICAge2F2YWlsYWJsZUxvY2FsZXMubWFwKChsb2NhbGVPcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xvY2FsZU9wdGlvbn0gb25DbGljaz17KCkgPT4gY2hhbmdlTG9jYWxlKGxvY2FsZU9wdGlvbil9PlxuICAgICAgICAgICAgICAgICAgICA8YT57bG9jYWxlT3B0aW9ufTwvYT5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2RldGFpbHM+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgPC91bD5cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJuYXZiYXItZW5kXCI+XG4gICAgICAgIHsvKiB0ZWxlcG9ydCBkZXN0ICovfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VUcmFuc2xhdGlvbnMiLCJ1c2VMb2NhbGUiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsIkxpbmsiLCJJbWFnZSIsIk5hdkJhciIsImNsYXNzTmFtZSIsInQiLCJsb2NhbGUiLCJyb3V0ZXIiLCJwYXRobmFtZSIsImF2YWlsYWJsZUxvY2FsZXMiLCJjaGFuZ2VMb2NhbGUiLCJuZXdMb2NhbGUiLCJuZXdQYXRoIiwicmVwbGFjZSIsInB1c2giLCJkaXYiLCJsYWJlbCIsInRhYkluZGV4Iiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJ1bCIsImxpIiwiaHJlZiIsImRldGFpbHMiLCJzdW1tYXJ5IiwibWFwIiwibG9jYWxlT3B0aW9uIiwib25DbGljayIsImEiLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NavBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/connect.ts":
/*!************************!*\
  !*** ./src/connect.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.7.7/node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/toast */ \"(ssr)/./src/utils/toast.ts\");\n\n\nconst relink = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: process.env.NEXT_PUBLIC_API_BASEURL || \"http://localhost:7001\",\n    withCredentials: true\n});\nrelink.interceptors.response.use(function(res) {\n    return res.data;\n}, function(error) {\n    if (error.response.status !== 401) {\n        _utils_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"Network error\");\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (relink);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29ubmVjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUI7QUFDUTtBQUVqQyxNQUFNRSxTQUFTRiw2Q0FBS0EsQ0FBQ0csTUFBTSxDQUFDO0lBQzFCQyxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLHVCQUF1QixJQUFJO0lBQ2hEQyxpQkFBaUI7QUFDbkI7QUFFQU4sT0FBT08sWUFBWSxDQUFDQyxRQUFRLENBQUNDLEdBQUcsQ0FDOUIsU0FBVUMsR0FBRztJQUNYLE9BQU9BLElBQUlDLElBQUk7QUFDakIsR0FDQSxTQUFVQyxLQUFLO0lBQ2IsSUFBSUEsTUFBTUosUUFBUSxDQUFDSyxNQUFNLEtBQUssS0FBSztRQUNqQ2Qsb0RBQUtBLENBQUNhLEtBQUssQ0FBQztJQUNkO0lBQ0EsT0FBT0UsUUFBUUMsTUFBTSxDQUFDSDtBQUN4QjtBQUdGLGlFQUFlWixNQUFNQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvY29ubmVjdC50cz8zMjJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcydcclxuaW1wb3J0IHRvYXN0IGZyb20gJy4vdXRpbHMvdG9hc3QnXHJcblxyXG5jb25zdCByZWxpbmsgPSBheGlvcy5jcmVhdGUoe1xyXG4gIGJhc2VVUkw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9CQVNFVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjcwMDEnLFxyXG4gIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcclxufSlcclxuXHJcbnJlbGluay5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKFxyXG4gIGZ1bmN0aW9uIChyZXMpIHtcclxuICAgIHJldHVybiByZXMuZGF0YVxyXG4gIH0sXHJcbiAgZnVuY3Rpb24gKGVycm9yKSB7XHJcbiAgICBpZiAoZXJyb3IucmVzcG9uc2Uuc3RhdHVzICE9PSA0MDEpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoJ05ldHdvcmsgZXJyb3InKVxyXG4gICAgfVxyXG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKVxyXG4gIH1cclxuKVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgcmVsaW5rXHJcbiJdLCJuYW1lcyI6WyJheGlvcyIsInRvYXN0IiwicmVsaW5rIiwiY3JlYXRlIiwiYmFzZVVSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfQkFTRVVSTCIsIndpdGhDcmVkZW50aWFscyIsImludGVyY2VwdG9ycyIsInJlc3BvbnNlIiwidXNlIiwicmVzIiwiZGF0YSIsImVycm9yIiwic3RhdHVzIiwiUHJvbWlzZSIsInJlamVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/connect.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/UserProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/UserProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _connect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/connect */ \"(ssr)/./src/connect.ts\");\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider({ children }) {\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUser = async ()=>{\n            try {\n                const res = await _connect__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/user\");\n                if (res.data && res.data.providerAccountId) {\n                    setUserData(res.data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch user:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUser();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            userData,\n            setUserData,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\providers\\\\UserProvider.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\nfunction useUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/UserProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/toast.ts":
/*!****************************!*\
  !*** ./src/utils/toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Simple toast implementation for React\nlet toastContainer = null;\nconst createToastContainer = ()=>{\n    if (!toastContainer) {\n        toastContainer = document.createElement(\"div\");\n        toastContainer.className = \"fixed top-4 right-4 z-50 space-y-2\";\n        document.body.appendChild(toastContainer);\n    }\n    return toastContainer;\n};\nconst showToast = (type, text, duration)=>{\n    const container = createToastContainer();\n    const toastElement = document.createElement(\"div\");\n    toastElement.className = `alert alert-${type} shadow-lg max-w-sm`;\n    toastElement.innerHTML = `\r\n    <div>\r\n      <span>${text}</span>\r\n    </div>\r\n  `;\n    container.appendChild(toastElement);\n    // Animate in\n    toastElement.style.opacity = \"0\";\n    toastElement.style.transform = \"translateX(100%)\";\n    setTimeout(()=>{\n        toastElement.style.transition = \"all 0.3s ease\";\n        toastElement.style.opacity = \"1\";\n        toastElement.style.transform = \"translateX(0)\";\n    }, 10);\n    // Remove after duration\n    setTimeout(()=>{\n        toastElement.style.opacity = \"0\";\n        toastElement.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (container.contains(toastElement)) {\n                container.removeChild(toastElement);\n            }\n        }, 300);\n    }, duration);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    warning: (text, duration = 2000)=>{\n        showToast(\"warning\", text, duration);\n    },\n    success: (text, duration = 2000)=>{\n        showToast(\"success\", text, duration);\n    },\n    error: (text, duration = 2000)=>{\n        showToast(\"error\", text, duration);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/toast.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7a4ab0f154ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2E5MmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3YTRhYjBmMTU0ZWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _components_NavBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NavBar */ \"(rsc)/./src/components/NavBar.tsx\");\n/* harmony import */ var _providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/UserProvider */ \"(rsc)/./src/providers/UserProvider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Mind Elixir Cloud\",\n    description: \"A powerful mind mapping application\"\n};\nasync function RootLayout({ children, params: { locale } }) {\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                messages: messages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_UserProvider__WEBPACK_IMPORTED_MODULE_3__.UserProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 w-screen z-10 my-5 pointer-events-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-3/5 m-auto rounded drop-shadow pointer-events-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QjtBQUMyQjtBQUNKO0FBQ047QUFDZTtBQUVoRCxNQUFNSSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLGVBQWVDLFdBQVcsRUFDdkNDLFFBQVEsRUFDUkMsUUFBUSxFQUFFQyxNQUFNLEVBQUUsRUFJbkI7SUFDQyxNQUFNQyxXQUFXLE1BQU1WLDREQUFXQTtJQUVsQyxxQkFDRSw4REFBQ1c7UUFBS0MsTUFBTUg7a0JBQ1YsNEVBQUNJO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNmLGlEQUFzQkE7Z0JBQUNXLFVBQVVBOzBCQUNoQyw0RUFBQ1IsaUVBQVlBOztzQ0FDWCw4REFBQ2E7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNiLDBEQUFNQTtnQ0FBQ2EsV0FBVTs7Ozs7Ozs7Ozs7d0JBRW5CUDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWluZC1lbGl4aXItY2xvdWQvLi9zcmMvYXBwL1tsb2NhbGVdL2xheW91dC50c3g/ODUzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIH0gZnJvbSAnbmV4dC1pbnRsJ1xuaW1wb3J0IHsgZ2V0TWVzc2FnZXMgfSBmcm9tICduZXh0LWludGwvc2VydmVyJ1xuaW1wb3J0IE5hdkJhciBmcm9tICdAL2NvbXBvbmVudHMvTmF2QmFyJ1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9wcm92aWRlcnMvVXNlclByb3ZpZGVyJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01pbmQgRWxpeGlyIENsb3VkJyxcbiAgZGVzY3JpcHRpb246ICdBIHBvd2VyZnVsIG1pbmQgbWFwcGluZyBhcHBsaWNhdGlvbicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbiAgcGFyYW1zOiB7IGxvY2FsZSB9XG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgcGFyYW1zOiB7IGxvY2FsZTogc3RyaW5nIH1cbn0pIHtcbiAgY29uc3QgbWVzc2FnZXMgPSBhd2FpdCBnZXRNZXNzYWdlcygpXG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPXtsb2NhbGV9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIDxOZXh0SW50bENsaWVudFByb3ZpZGVyIG1lc3NhZ2VzPXttZXNzYWdlc30+XG4gICAgICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgdy1zY3JlZW4gei0xMCBteS01IHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgPE5hdkJhciBjbGFzc05hbWU9XCJ3LTMvNSBtLWF1dG8gcm91bmRlZCBkcm9wLXNoYWRvdyBwb2ludGVyLWV2ZW50cy1hdXRvXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvVXNlclByb3ZpZGVyPlxuICAgICAgICA8L05leHRJbnRsQ2xpZW50UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiTmV4dEludGxDbGllbnRQcm92aWRlciIsImdldE1lc3NhZ2VzIiwiTmF2QmFyIiwiVXNlclByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwicGFyYW1zIiwibG9jYWxlIiwibWVzc2FnZXMiLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\app\[locale]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst metadata = {\n    title: \"Mind Elixir Cloud\",\n    description: \"A powerful mind mapping application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\git\\\\mind-elixir-cloud\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFTyxNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDRUg7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9taW5kLWVsaXhpci1jbG91ZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIk1pbmQgRWxpeGlyIENsb3VkXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkEgcG93ZXJmdWwgbWluZCBtYXBwaW5nIGFwcGxpY2F0aW9uXCIsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/NavBar.tsx":
/*!***********************************!*\
  !*** ./src/components/NavBar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\components\NavBar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || ![\n        \"en\",\n        \"cn\",\n        \"ja\"\n    ].includes(locale)) {\n        locale = \"en\";\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUVuRCxpRUFBZUEsNERBQWdCQSxDQUFDLE9BQU8sRUFBRUMsYUFBYSxFQUFFO0lBQ3RELHVEQUF1RDtJQUN2RCxJQUFJQyxTQUFTLE1BQU1EO0lBRW5CLHFDQUFxQztJQUNyQyxJQUFJLENBQUNDLFVBQVUsQ0FBQztRQUFDO1FBQU07UUFBTTtLQUFLLENBQUNDLFFBQVEsQ0FBQ0QsU0FBUztRQUNuREEsU0FBUztJQUNYO0lBRUEsT0FBTztRQUNMQTtRQUNBRSxVQUFVLENBQUMsTUFBTSx5RUFBTyxHQUFhLEVBQUVGLE9BQU8sTUFBTSxHQUFHRyxPQUFPO0lBQ2hFO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL21pbmQtZWxpeGlyLWNsb3VkLy4vc3JjL2kxOG4udHM/YmNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcidcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGdldFJlcXVlc3RDb25maWcoYXN5bmMgKHsgcmVxdWVzdExvY2FsZSB9KSA9PiB7XHJcbiAgLy8gVGhpcyB0eXBpY2FsbHkgY29ycmVzcG9uZHMgdG8gdGhlIGBbbG9jYWxlXWAgc2VnbWVudFxyXG4gIGxldCBsb2NhbGUgPSBhd2FpdCByZXF1ZXN0TG9jYWxlXHJcblxyXG4gIC8vIEVuc3VyZSB0aGF0IGEgdmFsaWQgbG9jYWxlIGlzIHVzZWRcclxuICBpZiAoIWxvY2FsZSB8fCAhWydlbicsICdjbicsICdqYSddLmluY2x1ZGVzKGxvY2FsZSkpIHtcclxuICAgIGxvY2FsZSA9ICdlbidcclxuICB9XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBsb2NhbGUsXHJcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi4vbWVzc2FnZXMvJHtsb2NhbGV9Lmpzb25gKSkuZGVmYXVsdFxyXG4gIH1cclxufSlcclxuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJyZXF1ZXN0TG9jYWxlIiwibG9jYWxlIiwiaW5jbHVkZXMiLCJtZXNzYWdlcyIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/providers/UserProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/UserProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserProvider: () => (/* binding */ e0),
/* harmony export */   useUser: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\providers\UserProvider.tsx#UserProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\git\mind-elixir-cloud\src\providers\UserProvider.tsx#useUser`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/@formatjs+icu-messageformat-parser@2.11.2","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.7.7","vendor-chunks/use-intl@3.26.5_react@18.3.1","vendor-chunks/intl-messageformat@10.7.16","vendor-chunks/tslib@2.8.1","vendor-chunks/@formatjs+icu-skeleton-parser@1.8.14","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.3.7","vendor-chunks/next-intl@3.26.5_next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1","vendor-chunks/form-data@4.0.1","vendor-chunks/asynckit@0.4.0","vendor-chunks/@formatjs+fast-memoize@2.2.7","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/mind-elixir@4.1.5_@types+node@20.16.11"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.29_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=C%3A%5Cgit%5Cmind-elixir-cloud%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cgit%5Cmind-elixir-cloud&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();